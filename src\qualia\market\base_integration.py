from __future__ import annotations

"""Módulo para integração com exchanges de criptomoedas via ``ccxt``."""

import os
import time
import math
import asyncio
import json
import contextlib
import random
import aiohttp
import websockets
import ccxt  # Adiciona a importação base do ccxt para referenciar tipos de exceção
import ccxt.async_support as ccxt_async
from datadog import DogStatsd
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from ..strategies.exceptions import InsufficientHistoryError
from ..utils.logger import get_logger
from ..utils.data_helpers import is_data_empty
from ..utils.timeframe import (
    timeframe_to_minutes,
    timeframe_to_pandas_freq,
    timeframe_to_milliseconds,
)
from ..utils.network_resilience import (
    wait_for_recovery,
    APICircuitBreaker,
)
from ..utils.tracing import get_tracer, instrument_logger, is_tracing_enabled
from typing import Any, Awaitable, Callable, Dict, List, Optional, Union, cast
from collections import defaultdict
from ..market.symbol_utils import normalize_symbol_async

from ..config import config, feature_toggle, load_market_defaults
from ..memory.event_bus import SimpleEventBus
from ..config.settings import get_env, market_metrics_enabled, settings
import inspect
from ..common.specs import MarketSpec
from ..utils.network_resilience import APICircuitBreaker

# Definições seguras para exceções do ccxt em ambientes de teste
try:  # pragma: no cover - garantia para stubs de testes
    CCXTNetworkError = ccxt.NetworkError
    CCXTExchangeError = ccxt.ExchangeError
except AttributeError:  # pragma: no cover - faltam exceções no stub
    CCXTNetworkError = Exception
    CCXTExchangeError = Exception


# Diretório para cache de dados
def cache_dir() -> str:
    """Retorna o diretório de cache atual definido em ``config``."""

    return config.cache_dir


os.makedirs(cache_dir(), exist_ok=True)

# Configuração de logging
# logging.basicConfig(
#     filename="logs/trading_system.log",
#     level=logging.INFO,
#     format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
# )
logger = get_logger(__name__)
if is_tracing_enabled():
    instrument_logger(logger.logger)


def _market_defaults() -> dict:
    """Return market configuration loaded from YAML."""
    return load_market_defaults()


def _default_global_rate_limit() -> float:
    return float(_market_defaults().get("rate_limits", {}).get("global", 5.0))


def _default_conn_timeout() -> float:
    return float(_market_defaults().get("timeouts", {}).get("connection", 30.0))


def _default_conn_retries() -> int:
    return int(_market_defaults().get("retries", {}).get("conn", 3))


def _default_ticker_timeout() -> float:
    return float(_market_defaults().get("timeouts", {}).get("ticker", 30.0))


def _default_ticker_retries() -> int:
    return int(_market_defaults().get("retries", {}).get("ticker", 3))


def _default_ohlcv_timeout() -> float:
    return float(_market_defaults().get("timeouts", {}).get("ohlcv", 90.0))


def _default_max_concurrent_requests() -> int:
    return int(
        _market_defaults().get("rate_limits", {}).get("max_concurrent_requests", 3)
    )


MIN_RATE_LIMIT = 4.0


__all__ = ["CryptoDataFetcher", "ConnectionInitializationError"]


class ConnectionInitializationError(Exception):
    """Raised when exchange connection cannot be initialized."""

    pass


def _to_dataframe(ohlcv: List[List[float]], columns: List[str]) -> pd.DataFrame:
    """Converte dados OHLCV da API em um DataFrame pandas."""
    try:
        # Verificar se ohlcv está vazio
        if not ohlcv:
            return pd.DataFrame(columns=columns)

        # Verificar se é uma lista de listas (formato esperado)
        if isinstance(ohlcv, list) and len(ohlcv) > 0:
            # Se o primeiro elemento é uma lista, usar formato padrão
            if isinstance(ohlcv[0], (list, tuple)):
                df = pd.DataFrame(ohlcv, columns=columns)
            # Se o primeiro elemento é um dicionário, converter
            elif isinstance(ohlcv[0], dict):
                df = pd.DataFrame(ohlcv)
                # Garantir que temos as colunas necessárias
                for col in columns:
                    if col not in df.columns:
                        df[col] = 0.0
                # Reordenar colunas
                df = df[columns]
            else:
                # Formato não reconhecido, tentar criar DataFrame diretamente
                df = pd.DataFrame(ohlcv, columns=columns)
        else:
            # Se não é uma lista, tentar criar DataFrame vazio
            return pd.DataFrame(columns=columns)

        # Converter timestamp para datetime
        if "timestamp" in df.columns and not df.empty:
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

        return df

    except Exception as e:
        logger.error(f"Erro ao converter OHLCV para DataFrame: {e}")
        logger.error(f"Tipo de dados recebido: {type(ohlcv)}")
        if ohlcv and len(ohlcv) > 0:
            logger.error(f"Primeiro elemento: {type(ohlcv[0])}, valor: {ohlcv[0]}")
        return pd.DataFrame(columns=columns)


class CryptoDataFetcher:
    """Wrapper genérico para interação com exchanges via ``ccxt``.

    Os resultados de ``fetch_ticker`` são mantidos em cache por um curto
    período para reduzir chamadas idempotentes à API.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        exchange_id: str = "kraken",
        rate_limit: Optional[float] = None,
        password: Optional[str] = None,
        conn_timeout: Optional[float] = None,
        conn_retries: Optional[int] = None,
        ticker_timeout: Optional[float] = None,
        ticker_retries: Optional[int] = None,
        ticker_backoff_base: Optional[float] = None,
        ohlcv_timeout: Optional[float] = None,
        use_websocket: bool = False,
        max_concurrent_requests: Optional[int] = None,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional[SimpleEventBus] = None,
        fail_threshold: int = 5,  # NETWORK FIX: Aumentado de 2 para 5 para maior tolerância a falhas de rede
        recovery_timeout: float = 120.0,  # NETWORK FIX: Aumentado para 120s para recuperação mais robusta
        symbol_fail_threshold: int = 3,
        symbol_recovery_timeout: float = 600.0,
        symbol_circuit_idle_seconds: float = 3600.0,
        config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Inicializa o fetcher de dados. Conexão e carregamento de mercados são feitos em initialize_connection.

        Parameters
        ----------
        api_key : str, optional
            Chave de API da exchange.
        api_secret : str, optional
            Segredo da API da exchange.
        exchange_id : str
            Identificador da exchange (ex.: ``"kraken"``).
        rate_limit : float, optional
            Intervalo mínimo entre chamadas consecutivas. Se ``None``, tenta
            ler ``RATE_LIMIT`` do ambiente e utiliza ``5.0`` como fallback.
        password : str, optional
            Senha/passphrase para exchanges que requerem.
        conn_timeout : float
            Timeout de conexão para ``load_markets``.
        conn_retries : int
            Número de tentativas de conexão.
        ticker_timeout : float, optional
            Tempo máximo para aguardar resposta de ``fetch_ticker``. Se
            ``None``, tenta ler ``TICKER_TIMEOUT`` do ambiente e usa ``7.0``
            como valor padrão.
        ticker_retries : int
            Quantidade de tentativas ao buscar ticker.
        ticker_backoff_base : float, optional
            Tempo base usado para o backoff exponencial entre tentativas.
            Se ``None`` utiliza ``TICKER_BACKOFF_BASE`` do ambiente ou ``1.0``.
        ohlcv_timeout : float, optional
            Tempo máximo para aguardar resposta de ``fetch_ohlcv``. Se ``None``
            utiliza ``OHLCV_TIMEOUT`` do ambiente ou ``10.0`` segundos.
        use_websocket : bool
            Se ``True``, tenta utilizar ``watch_ticker`` via WebSocket.
        max_concurrent_requests : int
            Número máximo de requisições simultâneas permitidas.
        statsd_client : Optional[DogStatsd]
            Cliente opcional para envio de métricas.
        event_bus : Optional[SimpleEventBus]
            Barramento opcional para publicação de eventos de mercado.
        fail_threshold : int
            Número de falhas consecutivas antes de abrir o ``CircuitBreaker``.
        recovery_timeout : float
            Tempo em segundos para tentar novas chamadas após o ``CircuitBreaker`` abrir.
        symbol_fail_threshold : int
            Número de falhas consecutivas por símbolo/timeframe antes de abrir
            o ``CircuitBreaker`` dedicado.
        symbol_recovery_timeout : float
            Intervalo de recuperação para o ``CircuitBreaker`` de cada
            símbolo/timeframe.
        symbol_circuit_idle_seconds : float
            Período máximo em segundos para manter ``symbol_circuits``
            inativo antes de remover a entrada. Use ``0`` para desativar.
        config : dict, optional
            Dicionário de configuração completo do sistema para acesso a parâmetros aninhados.
        """
        self.exchange_id = exchange_id  # Pode ser usado para logging ou lógica condicional se a classe for generalizada
        self.config = config or {}

        if rate_limit is None:
            try:
                rate_limit = float(
                    get_env("RATE_LIMIT", str(_default_global_rate_limit()), warn=False)
                )
            except ValueError:
                rate_limit = _default_global_rate_limit()

        # Aplicar rate limit mais conservador para produção
        min_rate_limit = max(settings.rate_limit, MIN_RATE_LIMIT)

        # Otimização específica para Kraken
        if exchange_id.lower() == "kraken":
            min_rate_limit = max(min_rate_limit, 5.0)  # Kraken requer mínimo 5s

        if rate_limit < min_rate_limit:
            logger.warning(
                "Rate limit solicitado de %.2fs inferior ao mínimo (%.1fs). Ajustando.",
                rate_limit,
                min_rate_limit,
            )
            rate_limit = min_rate_limit
        self.rate_limit = rate_limit
        self.last_request_time: float = 0.0
        self.last_rate_wait: float = 0.0
        self.last_candle_timestamp: Dict[str, Dict[str, int]] = defaultdict(dict)
        self.markets_loaded = False
        self._connection_active = False
        if conn_timeout is None:
            conn_timeout = _default_conn_timeout()
        if conn_retries is None:
            conn_retries = _default_conn_retries()
        self.conn_timeout = conn_timeout
        self.conn_retries = max(1, int(conn_retries))
        if ticker_timeout is None:
            try:
                ticker_timeout = float(
                    get_env(
                        "TICKER_TIMEOUT", str(_default_ticker_timeout()), warn=False
                    )
                )
            except ValueError:
                ticker_timeout = _default_ticker_timeout()

        # Otimização específica para Kraken
        if exchange_id.lower() == "kraken" and ticker_timeout < 30.0:
            ticker_timeout = 30.0  # Mínimo 30s para Kraken

        if ticker_timeout is not None and ticker_timeout <= 0:
            raise ValueError("ticker_timeout deve ser positivo")
        self.ticker_timeout = ticker_timeout
        # Otimização de retries para Kraken
        base_ticker_retries = _default_ticker_retries()
        if exchange_id.lower() == "kraken":
            base_ticker_retries = max(
                base_ticker_retries, 5
            )  # Mínimo 5 tentativas para Kraken

        self.ticker_retries = max(1, int(ticker_retries or base_ticker_retries))

        if ticker_backoff_base is None:
            try:
                ticker_backoff_base = float(
                    get_env("TICKER_BACKOFF_BASE", "1.0", warn=False)
                )
            except ValueError:
                ticker_backoff_base = 1.0

        # Otimização do backoff para Kraken
        if exchange_id.lower() == "kraken":
            ticker_backoff_base = max(ticker_backoff_base, 2.0)  # Mínimo 2s de backoff

        self.ticker_backoff_base = max(0.1, ticker_backoff_base)

        if ohlcv_timeout is None:
            try:
                ohlcv_timeout = float(
                    get_env("OHLCV_TIMEOUT", str(_default_ohlcv_timeout()), warn=False)
                )
            except ValueError:
                ohlcv_timeout = _default_ohlcv_timeout()

        # Otimização específica para Kraken
        if exchange_id.lower() == "kraken" and ohlcv_timeout < 90.0:
            ohlcv_timeout = 90.0  # Mínimo 90s para OHLCV do Kraken

        # Otimização específica para KuCoin - timeout mais agressivo
        if exchange_id.lower() == "kucoin" and ohlcv_timeout > 30.0:
            ohlcv_timeout = 30.0  # Máximo 30s para OHLCV da KuCoin (evita travamentos)

        if ohlcv_timeout is not None and ohlcv_timeout <= 0:
            raise ValueError("ohlcv_timeout deve ser positivo")
        self.ohlcv_timeout = ohlcv_timeout
        self._base_ohlcv_timeout = ohlcv_timeout
        self._consecutive_ohlcv_timeouts = 0
        # cache local para resultados de ticker
        self.ticker_cache: Dict[str, tuple[Dict[str, Any], float]] = {}
        try:
            self.ticker_cache_ttl = float(get_env("TICKER_CACHE_TTL", "10", warn=False))
        except ValueError:
            self.ticker_cache_ttl = 5.0
        
        # YAA: limite para trigger de refresh proativo do ticker cache
        try:
            self.ticker_refresh_threshold = float(
                os.getenv("TICKER_CACHE_REFRESH_THRESHOLD", "0.7")
            )
        except ValueError:
            self.ticker_refresh_threshold = 0.7
        self._ticker_refresh_tasks: Dict[str, asyncio.Task] = {}

        try:
            self.ohlcv_cache_ttl = float(get_env("OHLCV_CACHE_TTL", "60", warn=False))
        except ValueError:
            self.ohlcv_cache_ttl = 60.0
        self.ohlcv_cache: Dict[tuple[str, str], tuple[pd.DataFrame, float]] = {}
        # YAA: limite para trigger de refresh proativo do cache
        try:
            self.ohlcv_refresh_threshold = float(
                os.getenv("OHLCV_CACHE_REFRESH_THRESHOLD", "0.8")
            )
        except ValueError:
            self.ohlcv_refresh_threshold = 0.8
        self._ohlcv_refresh_tasks: Dict[MarketSpec, asyncio.Task] = {}
        try:
            base_ohlcv_retries = int(get_env("OHLCV_RETRIES", "3", warn=False))
        except ValueError:
            base_ohlcv_retries = 3

        # Otimização de retries OHLCV para Kraken
        if exchange_id.lower() == "kraken":
            base_ohlcv_retries = max(base_ohlcv_retries, 5)  # Mínimo 5 tentativas

        self.ohlcv_retries = base_ohlcv_retries

        try:
            self.ohlcv_backoff_base = float(
                get_env("OHLCV_BACKOFF_BASE", "1.0", warn=False)
            )
        except ValueError:
            self.ohlcv_backoff_base = 1.0

        # Otimização do backoff OHLCV para Kraken
        if exchange_id.lower() == "kraken":
            self.ohlcv_backoff_base = max(self.ohlcv_backoff_base, 3.0)  # Mínimo 3s
        self.use_websocket = use_websocket
        self._ws: Optional[Any] = None
        self._ws_task: Optional[asyncio.Task] = None
        self._ws_callback: Callable[..., Awaitable[None]] | None = None
        self.last_price: Optional[float] = None
        if max_concurrent_requests is None:
            max_concurrent_requests = _default_max_concurrent_requests()
        self.request_semaphore = asyncio.Semaphore(max_concurrent_requests)
        self._ohlcv_locks: Dict[tuple[str, str], asyncio.Lock] = {}
        self.symbol_circuits: Dict[tuple[str, str], APICircuitBreaker] = {}
        self.last_market_data_time: Optional[datetime] = None

        # Optional metrics client
        if statsd_client is not None or market_metrics_enabled:
            self.statsd: Optional[DogStatsd] = statsd_client or DogStatsd()
        else:
            self.statsd = None

        self.event_bus = event_bus
        # Toggle emission of ticker events with QUALIA_FT_MARKET_MODULE
        self.publish_ticker_events = feature_toggle("market_module")

        self.symbol_fail_threshold = max(1, int(symbol_fail_threshold))
        self.symbol_recovery_timeout = float(symbol_recovery_timeout)
        self.symbol_circuit_idle_seconds = float(symbol_circuit_idle_seconds)
        self._symbol_circuit_last_used: Dict[tuple[str, str], float] = {}
        self._symbol_circuit_cleanup_task: Optional[asyncio.Task] = None

        # Circuit breakers otimizados para pausar requisicoes apos falhas consecutivas
        # YAA TASK 4: Circuit Breaker otimizado com parâmetros mais agressivos
        self.ticker_circuit = APICircuitBreaker(
            fail_threshold, recovery_timeout, name="fetch_ticker"
        )
        self.ohlcv_circuit = APICircuitBreaker(
            fail_threshold, recovery_timeout, name="fetch_ohlcv"
        )

        try:
            self.ticker_failure_threshold = int(
                os.getenv("TICKER_FAILURE_THRESHOLD", str(fail_threshold))
            )
        except ValueError:
            self.ticker_failure_threshold = fail_threshold
        self._ticker_failure_count = 0

        # Metrics for timing instrumentation
        self.last_ticker_call_time = 0.0
        self.last_ticker_backoff = 0.0
        self.last_ticker_rate_wait = 0.0
        self.last_ticker_semaphore_wait = 0.0
        self.last_watch_call_time = 0.0
        self.last_watch_rate_wait = 0.0

        # --- Inicialização da instância ccxt ---
        # Configura parâmetros de fallback e cria a instância da exchange
        try:
            self.ohlcv_failure_threshold = int(
                os.getenv("OHLCV_FAILURE_THRESHOLD", "3")
            )
        except ValueError:
            self.ohlcv_failure_threshold = 3
        self.alternate_exchange_id = os.getenv("ALTERNATE_EXCHANGE")
        self._ohlcv_failure_count = 0

        try:
            self.earliest_timestamp_cooldown = float(
                get_env("EARLIEST_TIMESTAMP_COOLDOWN", "3600", warn=False)
            )
        except ValueError:
            self.earliest_timestamp_cooldown = 3600.0
        try:
            self.earliest_timestamp_threshold = float(
                os.getenv("EARLIEST_TIMESTAMP_THRESHOLD", "0")
            )
        except ValueError:
            self.earliest_timestamp_threshold = 0.0
        self._earliest_failure_ts: Dict[str, Dict[str, float]] = defaultdict(dict)

        # Track if the exchange supports fetchPositions
        self.fetch_positions_supported: Optional[bool] = None

        exchange_module = ccxt_async
        if self.use_websocket:
            try:
                import ccxt.pro as ccxt_pro  # type: ignore

                exchange_module = ccxt_pro
            except ImportError as exc:  # pragma: no cover - optional dependency
                logger.warning(
                    "ccxt.pro nao disponivel; prosseguindo sem suporte nativo a websocket: %s",
                    exc,
                )

        exchange_cls = getattr(exchange_module, self.exchange_id, None)
        if exchange_cls is None:
            raise ValueError(f"Exchange '{self.exchange_id}' não suportada pelo ccxt.")

        ccxt_params = {
            "apiKey": api_key or "",
            "secret": api_secret or "",
            "password": password or "",
            "enableRateLimit": True,
            "rateLimit": int(self.rate_limit * 1000),
            # YAA-FIX: Removido 'timeout' global. O timeout deve ser gerenciado
            # por chamada (e.g., em fetch_ticker, fetch_ohlcv) e não globalmente
            # na inicialização do cliente, pois isso causa timeouts prematuros em
            # chamadas lentas como a de candles.
            # "timeout": int(self.conn_timeout * 1000),
        }
        # Avoid logging sensitive credentials
        try:
            self.exchange = exchange_cls(ccxt_params)
        except TypeError:  # pragma: no cover - support stub classes without args
            self.exchange = exchange_cls()
        if self.use_websocket and not getattr(self.exchange, "has", {}).get(
            "watchTicker",
            False,
        ):
            logger.warning(
                "Exchange %s nao suporta watchTicker; usando REST", self.exchange_id
            )
            self.use_websocket = False

        if self.use_websocket:
            self.ticker_retries = 1

        # A chamada self.exchange.load_markets() foi movida para initialize_connection
        logger.info(
            (
                f"Instância assíncrona da exchange {self.exchange_id} criada. "
                "Chame initialize_connection() para carregar mercados."
            )
        )
        from .market_data_client import MarketDataClient
        from .order_executor import OrderExecutor
        from .websocket_manager import WebSocketManager

        self.market_data = MarketDataClient(self)
        self.order_executor = OrderExecutor(self)
        self.websocket_manager = WebSocketManager(self)
        self._initialized = False  # YAA: Flag para garantir inicialização única

        # Batch size dinâmico
        self._adaptive_batch_sizes: Dict[str, int] = {}  # Por timeframe
        self._last_request_latency: Dict[str, float] = {}  # Por timeframe

        # Registro de sessoes ``aiohttp`` adicionais utilizadas por componentes
        # auxiliares. Permite fechamento centralizado em :meth:`close`.
        self._aiohttp_sessions: Dict[str, aiohttp.ClientSession] = {}

        # YAA T7: Carrega o batch size para a KuCoin a partir da configuração
        self.kucoin_batch_size = (
            config.get("market", {}).get("kucoin_batch_size", 1000) if config else 1000
        )

    def _record_timing(self, name: str, duration: float) -> None:
        """Send a timing metric when ``statsd`` is configured."""

        if self.statsd is not None:
            self.statsd.timing(name, int(duration * 1000))

    def _increment_error(self, name: str) -> None:
        """Increment an error counter when ``statsd`` is configured."""

        if self.statsd is not None:
            self.statsd.increment(name)

    def _get_cached_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Return cached ticker when still valid and warn on stale data."""

        cached = self.ticker_cache.get(symbol)
        if not cached:
            logger.debug(f"[CACHE] Ticker cache miss para {symbol}")
            return None

        data, ts = cached
        age = time.time() - ts
        logger.debug(f"[CACHE] Ticker para {symbol}: idade={age:.1f}s, TTL={self.ticker_cache_ttl}s")
        
        if age > self.ticker_cache_ttl * 3:
            logger.warning(
                "Ticker em cache para %s está defasado há %.1fs", symbol, age
            )
        if self.ticker_cache_ttl <= 0 or age < self.ticker_cache_ttl:
            # YAA: Refresh proativo do ticker cache quando próximo de expirar
            if age >= self.ticker_cache_ttl * self.ticker_refresh_threshold:
                task = self._ticker_refresh_tasks.get(symbol)
                if task is None or task.done():
                    logger.info(
                        "Atualizacao proativa de ticker para %s",
                        symbol,
                    )
                    self._ticker_refresh_tasks[symbol] = asyncio.create_task(
                        self._refresh_ticker_async(symbol)
                    )
            logger.debug(f"[CACHE] Ticker cache hit para {symbol} (idade: {age:.1f}s)")
            return data
        
        logger.debug(f"[CACHE] Ticker expirado para {symbol} (idade: {age:.1f}s > TTL: {self.ticker_cache_ttl}s)")
        return None

    def _get_last_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Return last stored ticker ignoring TTL."""

        cached = self.ticker_cache.get(symbol)
        if cached:
            data, _ = cached
            return data
        return None

    def _store_ticker_cache(self, symbol: str, data: Dict[str, Any]) -> None:
        """Store ticker data in the local cache."""

        self.ticker_cache[symbol] = (data, time.time())
    
    async def _refresh_ticker_async(self, symbol: str) -> None:
        """YAA: Refresh ticker cache assincronamente para manter dados atualizados."""
        try:
            logger.debug(f"Iniciando refresh proativo do ticker para {symbol}")
            ticker_data = await self.fetch_ticker(symbol)
            if ticker_data:
                logger.debug(f"Ticker cache atualizado proativamente para {symbol}")
            else:
                logger.warning(f"Falha no refresh proativo do ticker para {symbol}")
        except Exception as exc:
            logger.warning(
                f"Erro no refresh proativo do ticker para {symbol}: {exc}",
                exc_info=True
            )

    def _get_cached_ohlcv(self, spec: MarketSpec) -> Optional[pd.DataFrame]:
        """Return cached OHLCV DataFrame when still valid and warn on stale data."""

        cached = self.ohlcv_cache.get(spec)
        if not cached:
            return None

        df, ts = cached
        age = time.time() - ts
        if age > self.ohlcv_cache_ttl * 3:
            logger.warning(
                "OHLCV em cache para %s está defasado há %.1fs",
                spec,
                age,
            )
        if age < self.ohlcv_cache_ttl:
            if age >= self.ohlcv_cache_ttl * self.ohlcv_refresh_threshold:
                task = self._ohlcv_refresh_tasks.get(spec)
                if task is None or task.done():
                    logger.info(
                        "Atualizacao proativa de OHLCV para %s@%s",
                        spec.symbol,
                        spec.timeframe,
                    )
                    self._ohlcv_refresh_tasks[spec] = asyncio.create_task(
                        self._fetch_ohlcv_impl(
                            spec,
                            since=None,
                            limit=self._max_ohlcv(spec.timeframe),
                            ohlcv_timeout=None,
                        )
                    )
            return df.copy()
        return None

    def _store_ohlcv_cache(self, spec: MarketSpec, df: pd.DataFrame) -> None:
        """Store OHLCV DataFrame in memory cache."""

        if not df.empty:
            self.ohlcv_cache[spec] = (df.copy(), time.time())

    def clean_future_timestamps(self) -> Dict[str, int]:
        """YAA: Limpa timestamps futuros do cache para evitar problemas de busca.

        Returns:
            Dict com estatísticas de limpeza
        """
        current_time_ms = int(time.time() * 1000)
        cleaned_count = 0
        cleaned_symbols: List[str] = []

        new_cache: Dict[str, Dict[str, int]] = {}
        for symbol, tf_map in list(self.last_candle_timestamp.items()):
            filtered = {tf: ts for tf, ts in tf_map.items() if ts <= current_time_ms}
            removed = len(tf_map) - len(filtered)
            if removed:
                cleaned_count += removed
                cleaned_symbols.append(symbol)
                logger.warning(
                    "Limpando %d timestamp(s) futuro(s) para %s (atual: %s)",
                    removed,
                    symbol,
                    current_time_ms,
                )
            if filtered:
                new_cache[symbol] = filtered

        self.last_candle_timestamp = new_cache

        stats = {
            "cleaned_count": cleaned_count,
            "cleaned_symbols": cleaned_symbols,
            "current_time_ms": current_time_ms,
        }

        if cleaned_count > 0:
            logger.info(
                "Limpeza de timestamps futuros concluída: %d timestamps limpos para %d símbolos",
                cleaned_count,
                len(cleaned_symbols),
            )

        return stats

    async def _load_cache(self, path: str) -> Optional[pd.DataFrame]:
        """Return cached DataFrame from ``path`` when available without blocking."""
        try:
            if not os.path.exists(path):
                return None

            # Verifica se o arquivo não está corrompido
            df = await asyncio.to_thread(pd.read_csv, path)
            if not all(
                col in df.columns
                for col in ["timestamp", "open", "high", "low", "close", "volume"]
            ):
                logger.warning("Arquivo de cache %s tem formato inválido", path)
                return None

            # Converte timestamp para datetime
            df["timestamp"] = pd.to_datetime(df["timestamp"], utc=True)

            # Verifica se o cache está completo
            if len(df) < 2:
                logger.warning("Cache %s tem menos de 2 registros", path)
                return None

            # Verifica se há duplicatas
            if len(df[df.duplicated(subset="timestamp")]) > 0:
                logger.warning("Cache %s contém timestamps duplicados", path)
                return None

            return df
        except (OSError, pd.errors.ParserError) as exc:
            logger.warning("Erro ao carregar cache %s: %s", path, exc)
            return None

    async def _save_cache(self, df: pd.DataFrame, path: str) -> None:
        """Save DataFrame to ``path`` ignoring errors without blocking."""

        if df.empty:
            return
        try:
            await asyncio.to_thread(df.to_csv, path, index=False)
        except (OSError, pd.errors.ParserError) as exc:  # pragma: no cover - I/O errors
            logger.warning("Erro ao salvar no cache %s: %s", path, exc)

    async def request_ohlcv_batch(
        self,
        spec: MarketSpec,
        since: int,
        limit: int,
    ) -> List[List[float]]:
        """Realiza a chamada à exchange para obter um lote de OHLCV.

        YAA TASK 2.3: Implementação melhorada com timeouts adaptativos e retry inteligente.
        """

        rl_start = time.perf_counter()
        await self._respect_rate_limit()
        wait = time.perf_counter() - rl_start
        logger.debug("Espera de rate limit OHLCV: %.2fs", wait)

        call_start = time.perf_counter()

        # YAA TASK 2.3: Timeout adaptativo baseado na exchange e histórico de performance
        batch_timeout = self.conn_timeout

        # Timeouts específicos por exchange baseados na experiência real
        exchange_timeouts = {
            "kraken": max(self.conn_timeout, 25.0),  # Kraken pode ser lenta
            "kucoin": max(
                self.conn_timeout, 60.0
            ),  # NETWORK FIX: KuCoin aumentado de 20.0 para 60.0 para resolver timeouts persistentes
            "binance": max(self.conn_timeout, 20.0),  # Binance geralmente rápida
            "coinbase": max(self.conn_timeout, 30.0),  # Coinbase moderada
        }

        batch_timeout = exchange_timeouts.get(self.exchange_id, self.conn_timeout)

        # Aumentar timeout se tivemos falhas recentes
        if (
            hasattr(self, "_consecutive_ohlcv_timeouts")
            and self._consecutive_ohlcv_timeouts > 0
        ):
            batch_timeout = min(batch_timeout * 1.5, 60.0)
            logger.debug(
                "Timeout aumentado para %.1fs devido a timeouts consecutivos",
                batch_timeout,
            )

        try:
            async with self.request_semaphore:
                raw_batch = await asyncio.wait_for(
                    self.exchange.fetch_ohlcv(
                        spec.symbol,
                        spec.timeframe,
                        since=since,
                        limit=limit,
                    ),
                    timeout=batch_timeout,
                )
        except asyncio.TimeoutError as exc:
            # YAA TASK 2.3: Logging melhorado para diagnóstico
            logger.warning(
                "Timeout (%.1fs) ao buscar OHLCV para %s %s (since=%s, limit=%s): %s",
                batch_timeout,
                spec.symbol,
                spec.timeframe,
                since,
                limit,
                exc,
            )
            raise
        except (CCXTNetworkError, CCXTExchangeError) as exc:
            # YAA TASK 2.3: Classificação de erros para retry inteligente
            error_msg = str(exc).lower()
            if "too many requests" in error_msg or "rate limit" in error_msg:
                logger.warning(
                    "Rate limit atingido ao buscar OHLCV para %s %s: %s",
                    spec.symbol,
                    spec.timeframe,
                    exc,
                )
            elif "network" in error_msg or "connection" in error_msg:
                logger.warning(
                    "Problema de rede ao buscar OHLCV para %s %s: %s",
                    spec.symbol,
                    spec.timeframe,
                    exc,
                )
            else:
                logger.error(
                    "Erro da exchange ao buscar OHLCV (%s) para %s %s: %s",
                    type(exc).__name__,
                    spec.symbol,
                    spec.timeframe,
                    exc,
                )
            raise

        # YAA TASK 2.3: Validação melhorada da resposta
        if not raw_batch:
            logger.debug(
                "Resposta vazia da exchange para %s %s", spec.symbol, spec.timeframe
            )
            return raw_batch

        # Ordenar por timestamp para consistência
        raw_batch.sort(key=lambda x: x[0])
        latency = time.perf_counter() - call_start

        # YAA TASK 2.3: Métricas de performance para otimização futura
        logger.debug(
            "Latência OHLCV: %.3fs, Timeout usado: %.1fs", latency, batch_timeout
        )
        logger.debug("Batch recebido: %d candles", len(raw_batch))

        if raw_batch:
            logger.debug(
                "Raw batch range: %s -> %s",
                datetime.fromtimestamp(raw_batch[0][0] / 1000),
                datetime.fromtimestamp(raw_batch[-1][0] / 1000),
            )

        # YAA TASK 2.3: Logging de URL simplificado
        req_url = getattr(self.exchange, "last_request_url", None)
        if not req_url:
            base_url = self.exchange.urls.get("api", {}).get("public", "")
            path_map = {
                "kraken": "/0/public/OHLC",
                "kucoin": "/api/v1/market/candles",
                "binance": "/api/v3/klines",
                "coinbase": "/products/{}/candles",
            }
            req_url = f"{base_url}{path_map.get(self.exchange_id, '')}"

        logger.debug(
            "OHLCV request: %s, symbol=%s, tf=%s, since=%s, limit=%s, latency=%.3fs",
            req_url,
            spec.symbol,
            spec.timeframe,
            since,
            limit,
            latency,
        )

        return raw_batch

    def advance_since(
        self, current_since: int, raw_batch: List[List[float]], tf_ms: int
    ) -> int:
        """Atualiza o ``since`` ao detectar duplicatas ou lotes vazios."""

        if raw_batch:
            new_since = raw_batch[-1][0] + tf_ms
        else:
            new_since = current_since + tf_ms
        logger.debug(
            "Avancando timestamp para proxima janela: %s",
            datetime.fromtimestamp(new_since / 1000),
        )
        return new_since

    async def _paginate_ohlcv(
        self,
        spec: MarketSpec,
        start_ts: int,
        end_ts: int,
        batch_size: int,
    ) -> List[List[float]]:
        """Fetch OHLCV data iterating over ``since`` until ``end_ts``.

        Consecutive ``asyncio.TimeoutError`` occurrences are monitored to
        dynamically adjust the request window. After ``TIMEOUT_THRESHOLD``
        timeouts, the ``dynamic_limit`` is halved to reduce the amount of
        requested candles. When the limit reaches ``1`` and new timeouts
        persist, ``current_since`` is advanced to skip the problematic
        interval.
        """

        # Medição de tempo para auto-tuning
        start_time = time.time()
        all_data: List[List[float]] = []
        # YAA FIX: evitar timestamps futuros que causam timeouts na API
        now_ms = int(time.time() * 1000)

        # Ajustar end_ts para o presente se estiver no futuro
        if end_ts > now_ms:
            logger.debug(
                "end_ts (%s) no futuro; ajustando para agora (%s)",
                datetime.fromtimestamp(end_ts / 1000),
                datetime.fromtimestamp(now_ms / 1000),
            )
            end_ts = now_ms

        # Ajustar start_ts se também estiver no futuro
        if start_ts > now_ms:
            timeframe_ms = self._timeframe_to_ms(spec.timeframe)
            start_ts = now_ms - (batch_size * timeframe_ms)
            logger.debug(
                "start_ts ajustado para evitar futuro: %s",
                datetime.fromtimestamp(start_ts / 1000),
            )

        current_since = start_ts
        prev_since = -1
        stalled_attempts = 0
        request_count = 0
        empty_responses = 0
        retry_count = 0
        timeout_attempts = 0
        env_limit = get_env("MAX_INITIAL_BATCH", None, warn=False)
        if env_limit is None:
            max_initial = self._max_ohlcv(spec.timeframe)
            # Kucoin: reduzir batch inicial para evitar timeouts longos
            if self.exchange_id == "kucoin":
                # YAA T7: Usa o batch size da configuração, com fallback
                kucoin_default_limit = self.config.get("market", {}).get(
                    "kucoin_batch_size", 1000
                )
                # Garante que não excederemos o máximo suportado pela própria exchange
                max_initial = min(max_initial, kucoin_default_limit)
                logger.info(
                    "Ajuste Kucoin: batch size inicial ajustado para %s candles",
                    kucoin_default_limit,
                )
        else:
            try:
                max_initial = int(env_limit)
            except (TypeError, ValueError):
                max_initial = self._max_ohlcv(spec.timeframe)
        cfg_limit = getattr(self.exchange, "max_initial_batch_size", None)
        if cfg_limit is not None:
            try:
                max_initial = int(cfg_limit)
            except (TypeError, ValueError) as exc:
                logger.warning(
                    "Valor invalido para max_initial_batch_size (%s): %s",
                    cfg_limit,
                    exc,
                )
        current_batch_size = min(batch_size, max_initial)
        TIMEOUT_THRESHOLD = 2
        timeframe_ms = self._timeframe_to_ms(spec.timeframe)
        # Quantidade total esperada de candles para este chunk (apenas para logs)
        total_expected = math.ceil((end_ts - start_ts) / timeframe_ms)

        while current_since < end_ts:
            remaining = max(0, end_ts - current_since)
            dynamic_limit = max(
                1, min(current_batch_size, math.ceil(remaining / timeframe_ms))
            )

            try:
                raw_batch = await self.request_ohlcv_batch(
                    spec, current_since, dynamic_limit
                )
                if raw_batch and raw_batch[0][0] > end_ts:
                    logger.warning("Lote fora do período solicitado; encerrando chunk")
                    break
                retry_count = 0
                timeout_attempts = 0
            except (asyncio.TimeoutError, CCXTNetworkError, CCXTExchangeError) as e:
                if isinstance(e, asyncio.TimeoutError):
                    timeout_attempts += 1
                    logger.warning(
                        "Timeout ao requisitar OHLCV para %s (%s). Tentativa %s/%s",
                        spec.symbol,
                        spec.timeframe,
                        timeout_attempts,
                        TIMEOUT_THRESHOLD,
                    )
                    # YAA TASK 2.3: Estratégia adaptativa melhorada para timeouts
                    if timeout_attempts >= TIMEOUT_THRESHOLD:
                        if current_batch_size > 1:
                            old_size = current_batch_size
                            current_batch_size = max(1, current_batch_size // 2)
                            logger.warning(
                                "Reduzindo batch size de %s para %s devido a timeouts consecutivos",
                                old_size,
                                current_batch_size,
                            )
                        else:
                            # Se já estamos no batch mínimo, pular esse período problemático
                            skip_interval = timeframe_ms * 5  # Pular 5 candles
                            current_since += skip_interval
                            logger.warning(
                                "Pulando intervalo problemático para %s após %s timeouts (novo since: %s)",
                                spec.symbol,
                                timeout_attempts,
                                datetime.fromtimestamp(current_since / 1000),
                            )
                        timeout_attempts = 0
                    # YAA TASK 2.3: Backoff exponencial para timeouts
                    backoff_delay = min(0.5 * (2**timeout_attempts), 5.0)
                    logger.debug(
                        "Aguardando %.2fs antes de retry após timeout", backoff_delay
                    )
                    await asyncio.sleep(backoff_delay)
                    raw_batch = []
                    continue
                # YAA TASK 2.3: Gestão melhorada de rate limiting
                if "Too many requests" in str(e) or "rate limit" in str(e).lower():
                    retry_count += 1
                    # Backoff exponencial mais inteligente para rate limits
                    wait_time = min(10 * (1.5**retry_count), 120)  # Máximo 2 minutos
                    logger.warning(
                        "Rate limit excedido (%s). Tentativa %s - aguardando %.1fs...",
                        type(e).__name__,
                        retry_count,
                        wait_time,
                    )
                    await asyncio.sleep(wait_time)
                    raw_batch = []
                    if retry_count >= 3:
                        logger.error(
                            "Rate limit persistente após %s tentativas: %s",
                            retry_count,
                            e,
                        )
                        # Pular para o próximo dia se rate limit persistir
                        skip_time = 24 * 60 * 60 * 1000  # 24 horas
                        current_since += skip_time
                        logger.warning(
                            "Pulando 24h devido a rate limit persistente (novo since: %s)",
                            datetime.fromtimestamp(current_since / 1000),
                        )
                        retry_count = 0  # Reset para próxima janela
                        await asyncio.sleep(1)  # Pausa pequena antes de continuar
                    continue
                else:
                    logger.error("Erro ao tentar novamente: %s", e)
                    current_since += 24 * 60 * 60 * 1000
                    raw_batch = []
                    await asyncio.sleep(0.05)
                    continue
            request_count += 1

            # Se a resposta da API veio vazia, encerramos ou tentamos avançar
            if not raw_batch:
                http_body = getattr(self.exchange, "last_http_response", None)
                if http_body == "":
                    logger.info(
                        "HTTP 200 com corpo vazio recebido. Encerrando paginacao."
                    )
                else:
                    logger.debug("Ultima resposta HTTP: %s", http_body)
                    break
                empty_responses += 1
                logger.warning(
                    "Resposta vazia da API para %s desde %s (tentativa %s)",
                    spec.symbol,
                    datetime.fromtimestamp(current_since / 1000),
                    empty_responses,
                )

                current_since = self.advance_since(
                    current_since, raw_batch, timeframe_ms
                )

                if empty_responses >= 3:
                    logger.info(
                        "Nenhum dado retornado apos multiplas tentativas. Encerrando paginacao."
                    )
                    break

                await asyncio.sleep(1)
                continue

            # Remove possíveis duplicatas em relação ao que já foi obtido
            if all_data:
                last_timestamp = all_data[-1][0]
                batch = [c for c in raw_batch if c[0] > last_timestamp]
            else:
                batch = raw_batch

            # Mesmo que todas as velas retornadas sejam duplicadas, isso indica que
            # ainda não progredimos o `since`. Portanto, avançamos manualmente,
            # mas não contamos como resposta vazia.
            if not batch:
                # Todos os candles retornados já estavam presentes em "all_data".
                # Para evitar loops infinitos avançando somente 1 timeframe (tf_ms),
                # saltamos diretamente para o candle imediatamente após o último
                # candle do lote recebido (caso "raw_batch" exista). Assim,
                # garantimos progresso significativo mesmo quando a Kraken retorna
                # dados sobrepostos.
                current_since = self.advance_since(
                    current_since, raw_batch, timeframe_ms
                )
                logger.debug(
                    "Batch duplicado detectado. Avançando since para: %s",
                    datetime.fromtimestamp(current_since / 1000),
                )
                stalled_attempts += 1
                if stalled_attempts >= 5:
                    logger.warning(
                        "Sem progresso apos %s tentativas. Encerrando paginacao.",
                        stalled_attempts,
                    )
                    break
                continue

            # Resetamos stalled_attempts quando recebemos dados novos
            stalled_attempts = 0

            # Filtra dados além de end_ts
            batch = [c for c in batch if c[0] <= end_ts]

            if raw_batch:
                logger.debug(
                    "Raw batch range: %s -> %s",
                    datetime.fromtimestamp(raw_batch[0][0] / 1000),
                    datetime.fromtimestamp(raw_batch[-1][0] / 1000),
                )

            if not batch:
                logger.warning(
                    "Todos os candles do lote estão fora do intervalo %s–%s",
                    start_ts,
                    end_ts,
                )

            all_data.extend(batch)

            # Log do total acumulado após gravar o batch
            logger.debug(
                "Batch gravado: %d candles (total: %d/%d)",
                len(batch),
                len(all_data),
                total_expected,
            )

            if request_count % 5 == 0 and batch:
                last_ts = all_data[-1][0]
                progress_pct = min(
                    100, ((last_ts - start_ts) / (end_ts - start_ts)) * 100
                )
                logger.info(
                    "Progresso do chunk: %.1f%% (%s pontos)",
                    progress_pct,
                    len(all_data),
                )

            if not batch:
                # Nenhum candle dentro de range util; avança janela mínima
                current_since = self.advance_since(
                    current_since, raw_batch, timeframe_ms
                )
                continue

            if batch[-1][0] >= end_ts:
                break

            current_since = batch[-1][0] + timeframe_ms

            if current_since <= prev_since:
                stalled_attempts += 1
                if stalled_attempts >= 3:
                    logger.warning(
                        "Timestamp since nao progrediu apos %s tentativas. Encerrando.",
                        stalled_attempts,
                    )
                    break
            else:
                stalled_attempts = 0

            prev_since = current_since

            if not batch and request_count > 10:
                logger.warning(
                    "Muitas tentativas sem dados adicionais. Finalizando chunk."
                )
                break

        # Auto-tuning baseado no tempo de execução
        if all_data and hasattr(self, "_auto_tune_batch_size"):
            elapsed_time = time.time() - start_time
            await self._auto_tune_batch_size(
                spec.timeframe, elapsed_time, len(all_data)
            )

        return all_data

    async def load_markets(self) -> Any:
        """Carrega os mercados da exchange de forma assíncrona, com cache."""
        logger.debug(
            "[DEBUG-QUALIA] Entrou em load_markets (markets_loaded=%s)",
            self.markets_loaded,
        )
        if not self.markets_loaded:
            logger.debug(
                "[DEBUG-QUALIA] Chamando self.exchange.load_markets()... (type=%s)",
                type(self.exchange),
            )
            markets = await self.exchange.load_markets()
            logger.debug(
                "[DEBUG-QUALIA] self.exchange.load_markets() retornou com sucesso (%d mercados)",
                len(markets) if hasattr(markets, "__len__") else -1,
            )
            self.markets_loaded = True
            return markets
        logger.debug("[DEBUG-QUALIA] Retornando mercados já carregados")
        return self.exchange.markets

    def __getattr__(self, name: str) -> Any:
        """Delegate attribute access to the underlying exchange instance.

        This avoids infinite recursion when ``exchange`` is missing by
        explicitly checking the internal ``__dict__`` for the attribute.
        """

        if name == "exchange":
            raise AttributeError("'CryptoDataFetcher' has no exchange instance")

        exchange = self.__dict__.get("exchange")
        if exchange is None:
            raise AttributeError("exchange not initialized")

        return getattr(exchange, name)

    def is_connected(self) -> bool:
        """Check if the exchange connection is still active.

        Returns
        -------
        bool
            ``True`` when the underlying ``aiohttp`` session exists and is not
            closed.
        """

        session = getattr(self.exchange, "session", None)
        return bool(session and not getattr(session, "closed", True))

    async def start_websocket(
        self,
        symbol: str,
        on_message: Callable[..., Awaitable[None]] | None = None,
        private: bool = False,
        reconnect_attempts: int = 3,
        reconnect_timeout: float = 10.0,
    ) -> None:
        """
        Connect to Kraken WebSocket API and listen for ticker updates.

        Args:
            symbol: Trading pair to subscribe to
            on_message: Callback for incoming messages
            private: Whether to use private feed
            reconnect_attempts: Number of reconnection attempts
            reconnect_timeout: Time to wait between reconnection attempts
        """
        del private  # not used but kept for interface compatibility

        self._ws_callback = on_message

        if self._ws_task and not self._ws_task.done():
            logger.info("WebSocket já está ativo. Ignorando nova conexão.")
            return

        attempt = 1
        while attempt <= reconnect_attempts:
            try:
                # Tenta reutilizar conexão existente
                if self._ws and not self._ws.closed:
                    logger.info("Reutilizando conexão WebSocket existente")
                    break

                # Nova conexão
                self._ws = await websockets.connect(
                    "wss://ws.kraken.com/v2",
                    ping_interval=20,  # Ping mais frequente para manter conexão
                    ping_timeout=10,  # Timeout do ping reduzido
                )
                subscribe = {
                    "method": "subscribe",
                    "params": {"channel": "ticker", "symbol": symbol},
                }
                await self._ws.send(json.dumps(subscribe))

                # Mantém a conexão ativa
                self._ws_task = asyncio.create_task(self._ticker_ws_loop(symbol))
                logger.info(
                    f"Conexão WebSocket estabelecida com sucesso (tentativa {attempt}/{reconnect_attempts})"
                )
                break

            except (
                asyncio.TimeoutError,
                websockets.exceptions.WebSocketException,
                OSError,
            ) as e:
                logger.warning(
                    f"Erro na tentativa {attempt}/{reconnect_attempts} de conexão: {str(e)}"
                )

                if attempt >= reconnect_attempts:
                    logger.error("Número máximo de tentativas de reconexão atingido")
                    raise

                # Aguarda antes de tentar novamente
                await asyncio.sleep(reconnect_timeout)
                attempt += 1

    async def _ticker_ws_loop(self, symbol: str) -> None:
        """Listen to ticker updates and keep the WebSocket alive."""
        if self._ws is None:
            raise RuntimeError("WebSocket connection not established")

        last_ping_time = time.time()
        ping_interval = 30.0

        try:
            while True:
                try:
                    # Mantém a conexão ativa enviando pings periódicos
                    if time.time() - last_ping_time > ping_interval:
                        await self._ws.ping()
                        last_ping_time = time.time()

                    message = await self._ws.recv()
                except asyncio.CancelledError:
                    logger.debug("Websocket cancelado")
                    break
                except websockets.ConnectionClosed:
                    logger.warning("Conexão WebSocket fechada. Tentando reconectar...")
                    await self.start_websocket(symbol, self._ws_callback)
                    break
                except websockets.WebSocketException as e:
                    logger.error(f"Erro inesperado no WebSocket: {e}")
                    raise
                else:
                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        continue

                    if (
                        isinstance(data, dict)
                        and data.get("channel") == "ticker"
                        and data.get("symbol") == symbol
                    ):
                        if self._ws_callback is not None:
                            await self._ws_callback(data)

                        price = data.get("price") or data.get("data", {}).get("price")
                        if price is None:
                            ask = data.get("data", {}).get("a")
                            if isinstance(ask, list):
                                price = ask[0]

                        try:
                            self.last_price = float(price)
                        except (TypeError, ValueError) as exc:
                            logger.exception(
                                "Erro ao converter preço recebido para float: %s",
                                exc,
                            )
        finally:
            if self._ws:
                try:
                    await self._ws.close()
                except OSError as e:
                    logger.warning(f"Erro ao fechar conexão: {e}")
                self._ws = None
                self._ws_task = None

    async def close_websocket(self) -> None:
        if self._ws_task:
            self._ws_task.cancel()
            if not self._ws_task.done():
                with contextlib.suppress(asyncio.CancelledError):
                    await self._ws_task
            self._ws_task = None
        if self._ws:
            await self._ws.close()
            self._ws = None

    def register_aiohttp_session(
        self, name: str, session: aiohttp.ClientSession
    ) -> None:
        """Register an ``aiohttp`` session for centralized cleanup."""

        self._aiohttp_sessions[name] = session

    async def initialize_connection(self) -> None:
        """Load exchange markets and establishes an initial connection.

        Can be called multiple times, but will only execute the connection
        logic once.
        """
        # --- YAA: Correção de Conexão Redundante (TASK-005) ---
        # Garante que a lógica de inicialização e carregamento de mercados
        # seja executada apenas uma vez para evitar conexões redundantes.
        if self._initialized:
            logger.debug(
                f"Conexão com {self.exchange_id} já inicializada. Ignorando chamada redundante."
            )
            return
        # --- Fim da Correção ---

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span("market.initialize_connection"):
            logger.info(f"Inicializando conexão com {self.exchange_id}...")
            start_time = time.perf_counter()
            logger.debug(
                "[DEBUG-QUALIA] Entrou em initialize_connection (exchange_id=%s, conn_retries=%s, conn_timeout=%s)",
                self.exchange_id,
                self.conn_retries,
                self.conn_timeout,
            )
            if self._connection_active and self.is_connected():
                logger.info(
                    "Conexão com %s já ativa; reutilizando mercados em cache",
                    self.exchange_id,
                )
                return
            attempt = 1
            last_error: Exception | None = None
            attempt_errors: list[str] = []
            while attempt <= self.conn_retries:
                try:
                    logger.debug(
                        "Tentando carregar mercados para %s (tentativa %s/%s)...",
                        self.exchange_id,
                        attempt,
                        self.conn_retries,
                    )
                    # --- Carregamento de mercados com cache local ---
                    if not self.exchange.markets:
                        cache_file = os.path.join(
                            cache_dir(), f"{self.exchange_id}_markets.json"
                        )
                        cache_loaded = False
                        if os.path.exists(cache_file):
                            try:
                                with open(cache_file, "r", encoding="utf-8") as fp:
                                    cached_markets = json.load(fp)
                                if isinstance(cached_markets, dict) and cached_markets:
                                    self.exchange.markets = cached_markets
                                    self.markets_loaded = True
                                    cache_loaded = True
                                    logger.info(
                                        "Mercados carregados do cache (%d símbolos)",
                                        len(cached_markets),
                                    )
                            except (OSError, json.JSONDecodeError) as exc:
                                logger.warning(
                                    "Falha ao carregar cache de mercados: %s", exc
                                )
                        if not cache_loaded:
                            # Fallback para chamada de rede
                            logger.debug(
                                "[DEBUG-QUALIA] Antes do await self.load_markets() (tentativa %d)",
                                attempt,
                            )
                            markets = await self.load_markets()
                            logger.debug(
                                "[DEBUG-QUALIA] Após await self.load_markets() (tentativa %d)",
                                attempt,
                            )
                            logger.info(
                                "load_markets concluído na tentativa %d com %d mercados",
                                attempt,
                                len(markets) if hasattr(markets, "__len__") else -1,
                            )
                            # Persistir cache para próximas execuções
                            try:
                                with open(cache_file, "w", encoding="utf-8") as fp:
                                    json.dump(self.exchange.markets, fp)
                                logger.debug(
                                    "Cache de mercados salvo em %s", cache_file
                                )
                            except OSError as exc:
                                logger.warning(
                                    "Nao foi possível salvar cache de mercados: %s", exc
                                )
                    else:
                        logger.debug(
                            "[DEBUG-QUALIA] Mercados em cache; pulando load_markets"
                        )
                        self.markets_loaded = True
                    rate_limit_attr = getattr(self.exchange, "rateLimit", 3000)
                    try:
                        rate_limit_value = float(rate_limit_attr) / 1000.0
                    except (TypeError, ValueError):
                        rate_limit_value = 3.0
                    self.rate_limit = max(self.rate_limit, rate_limit_value)
                    # Ajuste específico para Kucoin: garantir rate limit mínimo de 1s
                    if self.exchange_id == "kucoin" and self.rate_limit < 1.0:
                        old_limit = self.rate_limit
                        self.rate_limit = 1.0
                        logger.info(
                            "Ajuste Kucoin: rate limit atualizado de %.2fs para %.2fs",
                            old_limit,
                            self.rate_limit,
                        )
                    logger.info(
                        "Rate-limit ajustado dinamicamente para %.2fs", self.rate_limit
                    )
                    logger.info(
                        "Conexão assíncrona com %s estabelecida e mercados carregados.",
                        self.exchange_id,
                    )
                    self._record_timing(
                        "market.initialize_ms", time.perf_counter() - start_time
                    )
                    if self.event_bus:
                        self.event_bus.publish(
                            "exchange.lifecycle",
                            {
                                "exchange": self.exchange_id,
                                "event_type": "initialize",
                            },
                        )
                    if attempt_errors:
                        logger.warning(
                            "Conexão estabelecida após %s tentativas. Erros: %s",
                            attempt,
                            " | ".join(attempt_errors),
                        )

                    # YAA: Limpar timestamps futuros na inicialização
                    cleanup_stats = self.clean_future_timestamps()
                    if cleanup_stats["cleaned_count"] > 0:
                        logger.info(
                            "Timestamps futuros limpos na inicialização: %d",
                            cleanup_stats["cleaned_count"],
                        )

                    self._connection_active = True
                    self._initialized = True  # YAA: Marcar como inicializado
                    logger.info(
                        f" Conexão com {self.exchange_id} estabelecida com sucesso."
                    )
                    return
                except asyncio.TimeoutError as exc:
                    self._increment_error("market.initialize_error")
                    last_error = exc
                    attempt_errors.append(f"timeout na tentativa {attempt}")
                    logger.error(
                        "[DEBUG-QUALIA] TimeoutError em initialize_connection (tentativa %d, %d mercados carregados): %s",
                        attempt,
                        (
                            len(self.exchange.markets)
                            if hasattr(self.exchange, "markets")
                            else -1
                        ),
                        exc,
                    )
                except (ccxt.BaseError, AttributeError, Exception) as e:
                    self._increment_error("market.initialize_error")
                    last_error = e
                    attempt_errors.append(f"{type(e).__name__} na tentativa {attempt}")
                    logger.error(
                        "[DEBUG-QUALIA] Exception em initialize_connection (tentativa %d, %d mercados carregados): %s",
                        attempt,
                        (
                            len(self.exchange.markets)
                            if hasattr(self.exchange, "markets")
                            else -1
                        ),
                        e,
                    )

                if attempt >= self.conn_retries:
                    break
                wait_seconds = min(2 * attempt, self.conn_timeout)
                logger.debug(
                    "Aguardando %.0fs antes de nova tentativa de conexão...",
                    wait_seconds,
                )
                await asyncio.sleep(wait_seconds)
                attempt += 1
            if self.exchange:
                try:
                    await self.close()
                except ccxt.BaseError as exc:
                    logger.exception("Erro ao fechar conexão da exchange: %s", exc)
                finally:
                    self.exchange = None
            error_msg = (
                f"Não foi possível conectar à exchange {self.exchange_id} "
                f"após {self.conn_retries} tentativas"
            )
            if last_error is not None:
                error_msg += (
                    ". Último erro: " f"{last_error.__class__.__name__}: {last_error}"
                )
            logger.error(error_msg)
            raise ConnectionInitializationError(error_msg) from last_error

    async def close(self) -> None:
        """Fecha conexões e limpa recursos de forma abrangente."""
        logger.debug("Iniciando fechamento do CryptoDataFetcher...")
        await self.close_websocket()
        if self.exchange:
            try:
                # YAA TASK 5: Garantir o fechamento explícito da sessão aiohttp para evitar vazamentos
                if (
                    hasattr(self.exchange, "session")
                    and self.exchange.session
                    and not self.exchange.session.closed
                ):
                    logger.debug(
                        f"Fechando sessão aiohttp explícita para {self.exchange_id}"
                    )
                    await self.exchange.session.close()
                    logger.debug(f"Sessão aiohttp para {self.exchange_id} fechada.")

                close_method = getattr(self.exchange, "close", None)
                if close_method is not None:
                    if asyncio.iscoroutinefunction(close_method):
                        await close_method()
                    else:
                        close_method()
                    logger.info(f"Conexão com {self.exchange_id} fechada com sucesso.")

                if self.event_bus:
                    self.event_bus.publish(
                        "exchange.lifecycle",
                        {"exchange": self.exchange_id, "event_type": "shutdown"},
                    )
            except (ccxt.BaseError, OSError) as e:
                logger.error(
                    f"Erro ao fechar a conexão com a exchange {self.exchange_id}: {e}",
                    exc_info=True,
                )

            for session in list(self._aiohttp_sessions.values()):
                if not session.closed:
                    await session.close()
            self._aiohttp_sessions.clear()

        else:
            logger.info(f"Nenhuma conexão ativa com {self.exchange_id} para fechar.")
        if self._symbol_circuit_cleanup_task:
            self._symbol_circuit_cleanup_task.cancel()
            if not self._symbol_circuit_cleanup_task.done():
                with contextlib.suppress(asyncio.CancelledError):
                    await self._symbol_circuit_cleanup_task
            self._symbol_circuit_cleanup_task = None
        self._connection_active = False
        logger.debug("Finalizado fechamento do CryptoDataFetcher.")

    # ------------------------------------------------------------------
    def cleanup_symbol_circuits(self) -> int:
        """Remove entradas inativas de ``symbol_circuits``.

        Returns
        -------
        int
            Quantidade de circuitos removidos.
        """

        if self.symbol_circuit_idle_seconds <= 0:
            return 0

        now = time.time()
        stale_keys = [
            key
            for key, last in self._symbol_circuit_last_used.items()
            if now - last >= self.symbol_circuit_idle_seconds
        ]
        for key in stale_keys:
            self.symbol_circuits.pop(key, None)
            self._symbol_circuit_last_used.pop(key, None)
        if stale_keys:
            logger.debug("%d symbol circuits cleaned up", len(stale_keys))
        return len(stale_keys)

    # ------------------------------------------------------------------
    async def _symbol_circuit_cleanup_loop(self) -> None:
        """Background task to periodically clean ``symbol_circuits``."""

        try:
            while True:
                await asyncio.sleep(self.symbol_circuit_idle_seconds)
                self.cleanup_symbol_circuits()
        except asyncio.CancelledError:
            logger.debug("Symbol circuit cleanup task cancelled")

    def start_symbol_circuit_cleanup(self) -> None:
        """Inicia a tarefa assíncrona de limpeza de circuit breakers."""

        if (
            self.symbol_circuit_idle_seconds > 0
            and self._symbol_circuit_cleanup_task is None
        ):
            self._symbol_circuit_cleanup_task = asyncio.create_task(
                self._symbol_circuit_cleanup_loop()
            )

    async def _respect_rate_limit(self) -> None:
        """
        YAA NOTE: O rate limiter customizado foi desabilitado temporariamente.
        A lógica complexa anterior estava causando timeouts ao adicionar pausas
        excessivas no loop de eventos. A aplicação agora depende exclusivamente
        do rate limiter interno do `ccxt`, que se provou funcional no script
        de teste isolado.
        """
        return

    def _max_ohlcv(self, tf: str) -> int:
        """Retorna o número máximo de registros OHLCV que a exchange pode
        retornar em uma única chamada.

        Este método pode ser sobrecarregado por implementações de exchanges
        específicas para refletir seus limites.

        Parameters
        ----------
        tf : str
            O timeframe da consulta.

        Returns
        -------
        int
            O número máximo de registros.
        """
        adaptive_batch = self._adaptive_batch_sizes.get(tf)
        if adaptive_batch:
            return adaptive_batch

        # O valor padrão é 1000, mas pode ser ajustado
        return 1000

    def max_candles(self, timeframe: str) -> int:
        """Retorna o número máximo de candles que podem ser obtidos para um
        determinado timeframe.
        """
        return self._max_ohlcv(timeframe)

    async def fetch_ohlcv(
        self,
        spec: MarketSpec,
        since: Optional[int] = None,
        limit: int = 100,
        ohlcv_timeout: Optional[float] = None,
    ) -> pd.DataFrame:
        """Obtém dados OHLCV da exchange de forma assíncrona.

        Parameters
        ----------
        spec: MarketSpec
            Specification of the market data request.
        since : int, optional
            Timestamp in milliseconds.
        limit : int
            Number of candles to fetch.
        ohlcv_timeout : float, optional
            Maximum time to wait for the response. If exceeded, an empty
            DataFrame is returned.

        Returns
        -------
        pandas.DataFrame
            OHLCV data converted to :class:`~pandas.DataFrame`.

        Raises
        ------
        ccxt.NotImplementedForExchange
            If the exchange does not support the attribute ``_max_ohlcv``.

        Notes
        -----
        If all attempts fail, this method automatically executes
        ``fetch_historical_data`` as a fallback before returning an empty
        DataFrame. Starting from the current version, ``ohlcv_timeout`` is
        dynamically adjusted when ``TimeoutError`` occurs consecutively,
        potentially increasing up to three times the initial value to account
        for elevated latency periods. When ``since`` is close to the current
        time (i.e., fewer candles exist than ``limit``), ``limit`` is
        automatically reduced to the maximum number of candles available.
        """
        tracer = get_tracer(__name__)
        lock = self._ohlcv_locks.setdefault(
            (spec.symbol, spec.timeframe), asyncio.Lock()
        )
        with tracer.start_as_current_span(
            "market.fetch_ohlcv",
            attributes={"symbol": spec.symbol, "timeframe": spec.timeframe},
        ):
            async with lock:
                cached = self._get_cached_ohlcv(spec)
                if cached is not None:
                    return cached
                return await self._fetch_ohlcv_impl(
                    spec,
                    since=since,
                    limit=limit,
                    ohlcv_timeout=ohlcv_timeout,
                )

    async def _fetch_ohlcv_impl(
        self,
        spec: MarketSpec,
        since: Optional[int],
        limit: int,
        ohlcv_timeout: Optional[float],
    ) -> pd.DataFrame:
        """Implementação interna de :meth:`fetch_ohlcv`."""
        # YAA: Removido lock redundante que causava deadlock em chamadas simultâneas
        # O lock já é adquirido no método fetch_ohlcv() que chama esta implementação
        caps = getattr(self.exchange, "has", {})
        if isinstance(caps, dict) and not caps.get("fetchOHLCV", False):
            logger.warning("A exchange %s não suporta fetch_ohlcv.", self.exchange_id)
            return pd.DataFrame(
                columns=["timestamp", "open", "high", "low", "close", "volume"]
            )

        markets = getattr(self.exchange, "markets", None)
        if isinstance(markets, dict) and markets:
            try:
                symbol = await normalize_symbol_async(spec.symbol, self.exchange)
            except Exception as exc:
                logger.warning("Falha ao normalizar simbolo %s: %s", spec.symbol, exc)
        else:
            logger.debug(
                "Mercados nao disponiveis; usando simbolo fornecido: %s", spec.symbol
            )

        symbol_cb = self.symbol_circuits.setdefault(
            (spec.symbol, spec.timeframe),
            APICircuitBreaker(
                self.symbol_fail_threshold,
                self.symbol_recovery_timeout,
                name=f"ohlcv_{spec.symbol}_{spec.timeframe}",
            ),
        )
        self._symbol_circuit_last_used[(spec.symbol, spec.timeframe)] = time.time()

        if self.ohlcv_circuit.is_open or symbol_cb.is_open:
            logger.warning("Circuit breaker fetch_ohlcv aberto; tentando cache")
            cached_df = self._get_cached_ohlcv(spec)
            if cached_df is not None:
                logger.info(
                    "Retornando OHLCV de cache para %s@%s devido a circuito aberto",
                    spec.symbol,
                    spec.timeframe,
                )
                return cached_df
            await wait_for_recovery(self.ohlcv_circuit)
            await wait_for_recovery(symbol_cb)
        elif not self.ohlcv_circuit.allow_request() or not symbol_cb.allow_request():
            pause = max(
                self.ohlcv_circuit.time_until_recovery(),
                symbol_cb.time_until_recovery(),
            )
            logger.warning("Circuit breaker fetch_ohlcv ativo; aguardando %.2fs", pause)
            await wait_for_recovery(self.ohlcv_circuit)
            await wait_for_recovery(symbol_cb)

        base_wait = max(self.ohlcv_backoff_base, self.rate_limit)
        timeout = ohlcv_timeout if ohlcv_timeout is not None else self.ohlcv_timeout
        if timeout is None:
            timeout = self.conn_timeout

        # NETWORK FIX: Ajusta timeout para KuCoin baseado na latência real observada e problemas de rede
        if self.exchange_id.lower() == "kucoin" and timeout < 90.0:
            timeout = 90.0  # NETWORK FIX: Aumentado de 60s para 90s
            logger.debug("🔧 [NETWORK FIX] TIMEOUT AJUSTADO para KuCoin OHLCV: %.1fs", timeout)
        last_exc: Exception | None = None
        columns = ["timestamp", "open", "high", "low", "close", "volume"]

        max_limit = self._max_ohlcv(spec.timeframe)

        if limit > max_limit:
            # Paginação automática para limites maiores que o suportado pela Kraken
            timeframe_ms = self._timeframe_to_ms(spec.timeframe)
            if since is None:
                end_ts = int(time.time() * 1000)
                start_ts = end_ts - limit * timeframe_ms
            else:
                start_ts = since
                end_ts = since + limit * timeframe_ms

            logger.info(
                "Limit %s excede max %s – iniciando paginacao automatica (%s → %s)",
                limit,
                max_limit,
                datetime.fromtimestamp(start_ts / 1000).strftime("%Y-%m-%d %H:%M"),
                datetime.fromtimestamp(end_ts / 1000).strftime("%Y-%m-%d %H:%M"),
            )

            raw = await self._paginate_ohlcv(
                spec,
                start_ts,
                end_ts,
                max_limit,
            )
            df = _to_dataframe(raw, columns)
            if not df.empty:
                last_ts = int(df["timestamp"].iloc[-1].timestamp() * 1000)
                self.last_candle_timestamp.setdefault(spec.symbol, {})[
                    spec.timeframe
                ] = last_ts
                self._store_ohlcv_cache(spec, df)
                logger.debug("Paginacao completa. %s registros obtidos.", len(df))
                return df
            logger.warning(
                "Paginacao OHLCV retornou DataFrame vazio para %s", spec.symbol
            )

        empty_attempts = 0
        for attempt in range(1, self.ohlcv_retries + 1):
            logger.debug(
                "Tentativa %s/%s de fetch_ohlcv para %s@%s",
                attempt,
                self.ohlcv_retries,
                spec.symbol,
                spec.timeframe,
            )
            df = None
            try:
                await self._respect_rate_limit()
                last_ts = self.last_candle_timestamp.get(spec.symbol, {}).get(
                    spec.timeframe
                )
                request_since = since if since is not None else None
                if request_since is None and last_ts is not None:
                    # YAA: Validar se o timestamp não está no futuro
                    current_time_ms = int(time.time() * 1000)
                    if last_ts > current_time_ms:
                        logger.warning(
                            "Timestamp futuro detectado para %s@%s: %s (atual: %s). Limpando cache.",
                            spec.symbol,
                            spec.timeframe,
                            last_ts,
                            current_time_ms,
                        )
                        # Limpar timestamp inválido
                        if spec.symbol in self.last_candle_timestamp:
                            if (
                                spec.timeframe
                                in self.last_candle_timestamp[spec.symbol]
                            ):
                                del self.last_candle_timestamp[spec.symbol][
                                    spec.timeframe
                                ]
                        last_ts = None

                if request_since is None and last_ts is not None:
                    request_since = last_ts + 1
                
                # YAA-CORREÇÃO: Validar timestamp 'since' para evitar períodos com poucos dados
                if request_since is not None:
                    current_time_ms = int(time.time() * 1000)
                    timeframe_ms = self._timeframe_to_ms(spec.timeframe)
                    
                    # Calcular tempo mínimo necessário para obter dados suficientes
                    min_period_ms = limit * timeframe_ms  # Período necessário para 'limit' candles
                    time_since_request = current_time_ms - request_since
                    
                    # Se o período solicitado é muito pequeno (menos dados que o limit), resetar
                    if time_since_request < min_period_ms:
                        minutes_available = time_since_request / (60 * 1000)
                        minutes_needed = min_period_ms / (60 * 1000)
                        
                        logger.warning(
                            "PERÍODO INSUFICIENTE: since=%s permite apenas %.1f min de dados, mas precisamos de %.1f min para %s@%s (limit=%s). Resetando para buscar dados completos.",
                            request_since,
                            minutes_available,
                            minutes_needed,
                            spec.symbol,
                            spec.timeframe,
                            limit
                        )
                        # Limpar timestamp para forçar busca completa
                        if spec.symbol in self.last_candle_timestamp:
                            if spec.timeframe in self.last_candle_timestamp[spec.symbol]:
                                del self.last_candle_timestamp[spec.symbol][spec.timeframe]
                        request_since = None  # Forçar busca de dados recentes completos
                
                logger.info(
                    "Buscando dados OHLCV para %s, timeframe %s, limit %s, since %s",
                    spec.symbol,
                    spec.timeframe,
                    limit,
                    request_since,
                )
                logger.debug(
                    "Parametros da requisicao: since=%s, limit=%s",
                    request_since,
                    limit,
                )

                if request_since is not None:
                    timeframe_ms = self._timeframe_to_ms(spec.timeframe)
                    now_ms = int(time.time() * 1000)
                    potential = max(0, (now_ms - request_since) // timeframe_ms) + 1
                    if potential < limit:
                        logger.debug(
                            "Reducing limit from %s to %s based on current time",
                            limit,
                            potential,
                        )
                        limit = potential

                try:
                    call_start = time.perf_counter()

                    # DEBUG: Confirmação antes da chamada à exchange
                    logger.debug(
                        "🔍 PRE-FETCH: Iniciando chamada fetch_ohlcv para %s@%s (sem timeout wrapper)",
                        spec.symbol,
                        spec.timeframe,
                    )

                    if request_since is not None:
                        ohlcv = await self.exchange.fetch_ohlcv(
                            spec.symbol,
                            spec.timeframe,
                            since=request_since,
                            limit=limit,
                        )
                    else:
                        ohlcv = await self.exchange.fetch_ohlcv(
                            spec.symbol, spec.timeframe, limit=limit
                        )
                    latency = time.perf_counter() - call_start
                    self._record_timing("market.fetch_ohlcv_ms", latency)
                except AttributeError as exc:
                    if "_max_ohlcv" in str(exc):
                        raise ccxt.NotImplementedForExchange(str(exc)) from exc
                    fallback_limit = min(limit, 1500)
                    if request_since is not None:
                        ohlcv = await self.exchange.fetch_ohlcv(
                            spec.symbol,
                            spec.timeframe,
                            since=request_since,
                            limit=fallback_limit,
                        )
                    else:
                        ohlcv = await self.exchange.fetch_ohlcv(
                            spec.symbol, spec.timeframe, limit=fallback_limit
                        )

                # DEBUG: Confirmação que a chamada retornou
                logger.debug(
                    "🔍 FETCH_OHLCV: Chamada à exchange completada para %s@%s",
                    spec.symbol,
                    spec.timeframe,
                )

                # DEBUG: Verificação de tipo e tamanho
                logger.debug(
                    "🔍 FETCH_OHLCV: Tipo de resposta: %s, Tamanho: %s",
                    type(ohlcv).__name__,
                    len(ohlcv) if hasattr(ohlcv, "__len__") else "desconhecido",
                )

                try:
                    logger.debug(
                        "OHLCV bruto recebido (%s registros): %s",
                        len(ohlcv) if hasattr(ohlcv, "__len__") else "?",
                        json.dumps(ohlcv[:10] if len(ohlcv) > 10 else ohlcv),
                    )
                except Exception as exc:
                    logger.debug(
                        "OHLCV bruto recebido nao serializavel (%s): %s",
                        type(exc).__name__,
                        str(exc)[:200],
                    )

                # DEBUG: Antes da conversão para DataFrame
                logger.debug(
                    "🔍 FETCH_OHLCV: Convertendo para DataFrame %s@%s",
                    spec.symbol,
                    spec.timeframe,
                )

                df = _to_dataframe(ohlcv, columns)

                # DEBUG: Após conversão para DataFrame
                logger.debug(
                    "🔍 FETCH_OHLCV: DataFrame criado para %s@%s - Shape: %s, Empty: %s",
                    spec.symbol,
                    spec.timeframe,
                    df.shape if hasattr(df, "shape") else "desconhecido",
                    is_data_empty(df),
                )

                if is_data_empty(df):
                    logger.warning(
                        "🔍 FETCH_OHLCV: DataFrame vazio para %s@%s - levantando ValueError",
                        spec.symbol,
                        spec.timeframe,
                    )
                    raise ValueError("resposta vazia")

                num_primary = len(df)
                if limit is not None and num_primary < limit:
                    # Verifica se o número de candles retornados é menor que o solicitado
                    missing = limit - num_primary
                    missing_percent = (missing / limit) * 100

                    # Registra o evento com detalhes mais claros
                    logger.warning(
                        "AVISO DE LIMITE: Exchange %s retornou apenas %s/%s candles (%.1f%% do solicitado) para %s@%s",
                        self.exchange_id,
                        num_primary,
                        limit,
                        100 - missing_percent,
                        spec.symbol,
                        spec.timeframe,
                    )

                    # Registra métricas para monitoramento
                    if self.statsd:
                        self.statsd.gauge(
                            "market.candle_limit_percent",
                            100 - missing_percent,
                            tags=[
                                f"symbol:{spec.symbol}",
                                f"timeframe:{spec.timeframe}",
                                f"exchange:{self.exchange_id}",
                            ],
                        )

                    # YAA TASK 2: Política estrita de integridade de dados
                    # Rejeitar dados se menos de 80% do solicitado foi retornado
                    if missing_percent > 20:  # Mais de 20% faltando = rejeitar
                        logger.error(
                            "❌ DADOS REJEITADOS: Exchange %s retornou apenas %s/%s candles (%.1f%% do solicitado) para %s@%s. "
                            "Política de integridade: rejeitando dados incompletos.",
                            self.exchange_id,
                            num_primary,
                            limit,
                            100 - missing_percent,
                            spec.symbol,
                            spec.timeframe,
                        )

                        # Registrar falha no circuit breaker
                        self.ohlcv_circuit.record_failure()

                        # Emitir métrica de dados rejeitados
                        if self.statsd:
                            self.statsd.increment(
                                "market.data_rejected_incomplete",
                                tags=[
                                    f"symbol:{spec.symbol}",
                                    f"timeframe:{spec.timeframe}",
                                    f"exchange:{self.exchange_id}",
                                    f"completeness:{100 - missing_percent:.0f}",
                                ],
                            )

                        # Retornar DataFrame vazio para forçar uso de cache ou falha
                        return pd.DataFrame(columns=columns)

                    # Estratégia de fallback apenas para casos menos graves (10-20% faltando)
                    elif missing_percent > 10 and self.alternate_exchange_id:
                        # Caso moderado: tenta obter dados da exchange alternativa
                        timeframe_ms = self._timeframe_to_ms(spec.timeframe)
                        since_alt = (
                            int(
                                df["timestamp"].iloc[-1].timestamp() * 1000
                                + timeframe_ms
                            )
                            if not df.empty
                            else (
                                since
                                or int(time.time() * 1000) - (limit * timeframe_ms)
                            )
                        )

                        logger.info(
                            "Resposta incompleta de %s: %s/%s candles para %s@%s. Buscando %s restantes em %s.",
                            self.exchange_id,
                            num_primary,
                            limit,
                            spec.symbol,
                            spec.timeframe,
                            missing,
                            self.alternate_exchange_id,
                        )
                        alt_df = await self._fetch_from_alternate(
                            spec,
                            since_alt,
                            missing,
                            columns,
                        )
                        if not alt_df.empty:
                            df = (
                                pd.concat([df, alt_df])
                                .drop_duplicates(subset="timestamp")
                                .sort_values("timestamp")
                                .reset_index(drop=True)
                            )
                            logger.info(
                                "Série OHLCV para %s@%s completada com %s candles da %s",
                                spec.symbol,
                                spec.timeframe,
                                len(alt_df),
                                self.alternate_exchange_id,
                            )
                    elif missing_percent > 20:
                        # Caso moderado: tenta preencher com dados históricos do cache
                        logger.info(
                            "Tentando complementar dados de %s@%s com cache histórico",
                            spec.symbol,
                            spec.timeframe,
                        )
                        cached_df = self._get_cached_ohlcv(spec)
                        if cached_df is not None and not cached_df.empty:
                            df = (
                                pd.concat([cached_df, df])
                                .drop_duplicates(subset="timestamp")
                                .sort_values("timestamp")
                                .reset_index(drop=True)
                            )
                            logger.info(
                                "Dados complementados com cache: agora temos %s candles para %s@%s",
                                len(df),
                                spec.symbol,
                                spec.timeframe,
                            )
                    else:
                        # Caso leve: apenas registra o evento
                        logger.info(
                            "Continuando com dados parciais (%s/%s candles) para %s@%s",
                            num_primary,
                            limit,
                            spec.symbol,
                            spec.timeframe,
                        )

                last_ts = int(df["timestamp"].iloc[-1].timestamp() * 1000)
                self.last_candle_timestamp.setdefault(spec.symbol, {})[
                    spec.timeframe
                ] = last_ts
                self._ohlcv_failure_count = 0
                symbol_cb.record_success()
                self.ohlcv_circuit.record_success()
                self.last_market_data_time = datetime.now(timezone.utc)
                self._consecutive_ohlcv_timeouts = 0
                self.ohlcv_timeout = self._base_ohlcv_timeout
                logger.debug("Dados OHLCV obtidos. Tamanho: %s registros", len(df))
                self._store_ohlcv_cache(spec, df)
                return df

            except asyncio.CancelledError:
                logger.info(
                    "fetch_ohlcv cancelado para %s %s", spec.symbol, spec.timeframe
                )
                raise
            except asyncio.TimeoutError as exc:
                symbol_cb.record_failure()
                self.ohlcv_circuit.record_failure()
                self._consecutive_ohlcv_timeouts += 1
                self._increment_error("market.fetch_ohlcv_error")
                logger.warning(
                    "Timeout ao buscar OHLCV para %s %s: %s: %s",
                    spec.symbol,
                    spec.timeframe,
                    exc.__class__.__name__,
                    exc,
                )
                timeframe_ms = self._timeframe_to_ms(spec.timeframe)
                req_base = request_since if request_since is not None else 0
                last_req = req_base + limit * timeframe_ms
                if df:
                    self.last_candle_timestamp.setdefault(spec.symbol, {})[
                        spec.timeframe
                    ] = last_req
                    logger.debug(
                        "last_candle_timestamp atualizado apos timeout: %s",
                        last_req,
                    )
                if self._consecutive_ohlcv_timeouts >= 2:
                    new_timeout = min(
                        self.ohlcv_timeout * 1.5, self._base_ohlcv_timeout * 3
                    )
                    if new_timeout > self.ohlcv_timeout:
                        self.ohlcv_timeout = new_timeout
                        logger.warning(
                            "Aumentando ohlcv_timeout para %.1fs apos %s timeouts consecutivos",
                            self.ohlcv_timeout,
                            self._consecutive_ohlcv_timeouts,
                        )
                last_exc = exc  # Armazena exceção para possível fallback posterior
                # Aplica backoff exponencial semelhante a outras exceções
                if attempt < self.ohlcv_retries:
                    delay = min(base_wait * 2 ** (attempt - 1), 30)
                    delay += random.uniform(0, 1)
                    logger.debug("Reagendando em %.2fs", delay)
                    await asyncio.sleep(delay)
                    continue  # prossiga para a próxima tentativa
                # Caso tenha excedido o número de tentativas, verifica cache antes de retornar
                cached_df = self._get_cached_ohlcv(spec)
                if cached_df is not None:
                    logger.info(
                        "Dados de cache utilizados para %s %s",
                        spec.symbol,
                        spec.timeframe,
                    )
                    return cached_df
                # Se nenhum cache disponível, quebra o laço para acionar fallback global
                break
            except ValueError as exc:
                symbol_cb.record_failure()
                self.ohlcv_circuit.record_failure()
                last_exc = exc
                self._ohlcv_failure_count += 1
                self._increment_error("market.fetch_ohlcv_error")
                self._consecutive_ohlcv_timeouts = 0
                # YAA TASK 2.3: Gestão melhorada de respostas vazias
                if str(exc) == "resposta vazia":
                    empty_attempts += 1
                    logger.warning(
                        "Tentativa %s/%s retornou vazio para %s %s (total vazios: %s)",
                        attempt,
                        self.ohlcv_retries,
                        spec.symbol,
                        spec.timeframe,
                        empty_attempts,
                    )
                    # Se muitas respostas vazias, tentar com parâmetros diferentes
                    if empty_attempts >= 2 and attempt < self.ohlcv_retries:
                        # Tentar reduzir o limit na próxima tentativa
                        original_limit = limit
                        limit = max(limit // 2, 1)
                        logger.info(
                            "Reduzindo limit de %s para %s devido a respostas vazias consecutivas",
                            original_limit,
                            limit,
                        )
                else:
                    logger.warning(
                        "Tentativa %s/%s falhou em fetch_ohlcv para %s %s: %s: %s",
                        attempt,
                        self.ohlcv_retries,
                        spec.symbol,
                        spec.timeframe,
                        type(exc).__name__,
                        exc,
                    )
                if attempt < self.ohlcv_retries:
                    # YAA TASK 2.3: Backoff inteligente baseado no tipo de erro
                    if str(exc) == "resposta vazia":
                        # Menor delay para respostas vazias
                        delay = min(base_wait * 1.2 ** (attempt - 1), 15)
                    else:
                        # Delay normal para outros erros
                        delay = min(base_wait * 2 ** (attempt - 1), 30)

                    delay += random.uniform(0, 0.5)  # Jitter reduzido
                    logger.debug(
                        "Reagendando em %.2fs (tipo: %s)", delay, type(exc).__name__
                    )
                    await asyncio.sleep(delay)
                # prossegue para a proxima tentativa ou encerra
            except (
                CCXTNetworkError,
                CCXTExchangeError,
                Exception,
            ) as exc:
                symbol_cb.record_failure()
                self.ohlcv_circuit.record_failure()
                self._increment_error("market.fetch_ohlcv_error")
                last_exc = exc
                self._ohlcv_failure_count += 1
                self._consecutive_ohlcv_timeouts = 0
                if "Too many requests" in str(exc):
                    self.rate_limit = min(self.rate_limit * 1.5, 60.0)
                logger.warning(
                    "Tentativa %s/%s falhou em fetch_ohlcv para %s %s: %s: %s",
                    attempt,
                    self.ohlcv_retries,
                    spec.symbol,
                    spec.timeframe,
                    type(exc).__name__,
                    exc,
                    exc_info=True,
                )
                if attempt < self.ohlcv_retries:
                    delay = min(base_wait * 2 ** (attempt - 1), 30)
                    delay += random.uniform(0, 1)
                    logger.debug("Reagendando em %.2fs", delay)
                    await asyncio.sleep(delay)

        # Falhou apos max tentativas
        if empty_attempts == self.ohlcv_retries and self.alternate_exchange_id:
            try:
                alt_cls = getattr(ccxt_async, self.alternate_exchange_id)
                alt = alt_cls({"enableRateLimit": True})
                alt_symbol = await normalize_symbol_async(spec.symbol, alt)
                alt_data = await alt.fetch_ohlcv(
                    alt_symbol, spec.timeframe, since=since, limit=limit
                )
                await alt.close()
                df = _to_dataframe(alt_data, columns)
                if not df.empty:
                    last_ts = int(df["timestamp"].iloc[-1].timestamp() * 1000)
                    self.last_candle_timestamp.setdefault(spec.symbol, {})[
                        spec.timeframe
                    ] = last_ts
                    self._ohlcv_failure_count = 0
                    self._consecutive_ohlcv_timeouts = 0
                    self.ohlcv_timeout = self._base_ohlcv_timeout
                    symbol_cb.record_success()
                    self.last_market_data_time = datetime.now(timezone.utc)
                    self._store_ohlcv_cache(spec, df)
                    logger.debug(
                        "OHLCV obtido da exchange alternativa %s",
                        self.alternate_exchange_id,
                    )
                    return df
            except (
                CCXTNetworkError,
                CCXTExchangeError,
                asyncio.TimeoutError,
            ) as exc:  # pragma: no cover - network issues
                logger.warning(
                    "Falha ao buscar OHLCV na exchange alternativa %s: %s",
                    self.alternate_exchange_id,
                    exc,
                )
                self.ohlcv_circuit.record_failure()

        if (
            self._ohlcv_failure_count >= self.ohlcv_failure_threshold
            and self.alternate_exchange_id
        ):
            try:
                alt_cls = getattr(ccxt_async, self.alternate_exchange_id)
                alt = alt_cls({"enableRateLimit": True})
                alt_symbol = await normalize_symbol_async(spec.symbol, alt)
                alt_data = await alt.fetch_ohlcv(
                    alt_symbol, spec.timeframe, since=since, limit=limit
                )
                await alt.close()
                df = _to_dataframe(alt_data, columns)
                if not df.empty:
                    last_ts = int(df["timestamp"].iloc[-1].timestamp() * 1000)
                    self.last_candle_timestamp.setdefault(spec.symbol, {})[
                        spec.timeframe
                    ] = last_ts
                    self._ohlcv_failure_count = 0
                    self._consecutive_ohlcv_timeouts = 0
                    self.ohlcv_timeout = self._base_ohlcv_timeout
                    symbol_cb.record_success()
                    self.last_market_data_time = datetime.now(timezone.utc)
                    self._store_ohlcv_cache(spec, df)
                    logger.debug(
                        "OHLCV obtido da exchange alternativa %s",
                        self.alternate_exchange_id,
                    )
                    return df
            except (
                CCXTNetworkError,
                CCXTExchangeError,
                asyncio.TimeoutError,
            ) as exc:  # pragma: no cover - network issues
                logger.warning(
                    "Falha ao buscar OHLCV na exchange alternativa %s: %s",
                    self.alternate_exchange_id,
                    exc,
                )
                self.ohlcv_circuit.record_failure()

        logger.warning(
            "fetch_ohlcv failed %s vezes para %s %s; verificando cache",
            self.ohlcv_retries,
            spec.symbol,
            spec.timeframe,
        )
        cached_df = self._get_cached_ohlcv(spec)
        if cached_df is not None:
            logger.info(
                "Dados de cache utilizados para %s %s", spec.symbol, spec.timeframe
            )
            return cached_df
        if last_exc is not None:
            logger.error("Ultimo erro em fetch_ohlcv: %s", last_exc)
            try:
                if hasattr(self, "fetch_historical_data"):
                    df = await self.fetch_historical_data(spec)
                    if not df.empty:
                        self._store_ohlcv_cache(spec, df)
                    return df
            except (
                CCXTNetworkError,
                CCXTExchangeError,
                asyncio.TimeoutError,
            ) as exc:  # pragma: no cover - network issues
                logger.warning(
                    "Fallback fetch_historical_data falhou: %s",
                    exc,
                )
        symbol_cb.record_failure()
        self.ohlcv_circuit.record_failure()
        return pd.DataFrame(columns=columns)

    async def _fetch_from_alternate(
        self,
        spec: MarketSpec,
        since: int,
        limit: int,
        columns: List[str],
    ) -> pd.DataFrame:
        """Fetch remaining candles from the configured alternate exchange."""

        try:
            alt_cls = getattr(ccxt_async, self.alternate_exchange_id)
            alt = alt_cls({"enableRateLimit": True})
            alt_symbol = await normalize_symbol_async(spec.symbol, alt)
            alt_data = await alt.fetch_ohlcv(
                alt_symbol, spec.timeframe, since=since, limit=limit
            )
            await alt.close()
            return _to_dataframe(alt_data, columns)
        except (
            CCXTNetworkError,
            CCXTExchangeError,
            asyncio.TimeoutError,
        ) as exc:  # pragma: no cover - network issues
            logger.warning(
                "Falha ao buscar OHLCV na exchange alternativa %s: %s",
                self.alternate_exchange_id,
                exc,
            )
            self.ohlcv_circuit.record_failure()
            return pd.DataFrame(columns=columns)

    async def watch_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Obtém o ticker via WebSocket se suportado ou faz fallback para REST."""

        rl_start = time.perf_counter()
        await self._respect_rate_limit()
        self.last_watch_rate_wait = time.perf_counter() - rl_start
        logger.debug(
            "Aguardar rate limit %.2fs antes de watch_ticker %s",
            self.last_watch_rate_wait,
            symbol,
        )
        try:
            normalized_symbol = await normalize_symbol_async(symbol, self.exchange)
        except ValueError as exc:
            logger.error("Símbolo inválido %s: %s", symbol, exc)
            return None
        if not (self.use_websocket and self.exchange.has.get("watchTicker", False)):
            call_start = time.perf_counter()
            result = cast(
                Dict[str, Any],
                await asyncio.wait_for(
                    self.exchange.fetch_ticker(normalized_symbol),
                    timeout=self.ticker_timeout,
                ),
            )
            self.last_watch_call_time = time.perf_counter() - call_start
            return result

        logger.info(
            "[watch_ticker] Chamando watch_ticker para: %s (timeout=%ss)",
            normalized_symbol,
            self.ticker_timeout,
        )
        try:
            start_time = time.perf_counter()
            ticker = cast(
                Dict[str, Any],
                await asyncio.wait_for(
                    self.exchange.watch_ticker(normalized_symbol),
                    timeout=self.ticker_timeout,
                ),
            )
            self.last_watch_call_time = time.perf_counter() - start_time
            logger.info(
                "[watch_ticker] Tempo de resposta para %s: %.2fs",
                normalized_symbol,
                self.last_watch_call_time,
            )
            logger.debug(
                "Lat\u00eancia watch_ticker %s: %.2fs",
                normalized_symbol,
                self.last_watch_call_time,
            )
            if ticker and any(
                k in ticker for k in ("last", "close", "bid", "ask", "price")
            ):
                return ticker
            logger.warning(
                "Ticker websocket invalido para %s: %s", normalized_symbol, ticker
            )
        except (
            asyncio.TimeoutError,
            CCXTNetworkError,
            websockets.exceptions.WebSocketException,
        ) as exc:
            logger.warning(
                "watch_ticker falhou para %s: %s",
                normalized_symbol,
                exc.__class__.__name__,
                exc_info=True,
            )

        # fallback para REST
        call_start = time.perf_counter()
        try:
            result = cast(
                Dict[str, Any],
                await asyncio.wait_for(
                    self.exchange.fetch_ticker(normalized_symbol),
                    timeout=self.ticker_timeout,
                ),
            )
        except (
            asyncio.TimeoutError,
            CCXTNetworkError,
            CCXTExchangeError,
        ) as exc:
            logger.warning(
                "Fallback fetch_ticker falhou para %s: %s",
                normalized_symbol,
                exc.__class__.__name__,
            )
            return None
        self.last_watch_call_time = time.perf_counter() - call_start
        return result

    async def fetch_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Busca o ticker mais recente com tentativas e backoff exponencial.

        A função tenta utilizar ``watch_ticker`` caso a exchange suporte
        WebSocket. Em seguida, faz ``fetch_ticker`` via REST aplicando
        backoff exponencial para lidar com erros ou timeouts. Utilizar
        ``ticker_timeout`` mais curto aliado a mais tentativas pode reduzir
        a latência percebida.
        """

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "market.fetch_ticker.start",
            attributes={"symbol": symbol, "exchange": self.exchange_id},
        ):
            pass

        with tracer.start_as_current_span(
            "market.fetch_ticker",
            attributes={"symbol": symbol, "exchange": self.exchange_id},
        ):
            result = await self._fetch_ticker_impl(symbol)

        with tracer.start_as_current_span(
            "market.fetch_ticker.end",
            attributes={"symbol": symbol, "exchange": self.exchange_id},
        ):
            pass

        return result

    async def _fetch_ticker_impl(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Implementação interna de :meth:`fetch_ticker`."""

        if not self.ticker_circuit.allow_request():
            pause = self.ticker_circuit.time_until_recovery()
            logger.warning(
                "Circuit breaker fetch_ticker aberto; retornando cache (%.2fs para liberar)",
                pause,
            )
            if self.event_bus:
                self.event_bus.publish(
                    "market.ticker_pause",
                    {"symbol": symbol, "delay": pause},
                )
            self.ticker_circuit.record_failure()
            cached = self._get_cached_ticker(symbol)
            if cached is not None:
                logger.debug(
                    "Retornando ticker em cache para %s devido a breaker aberto",
                    symbol,
                )
                return cached
            fallback_fn = getattr(self, "_use_ohlcv_fallback", None)
            if callable(fallback_fn):
                return fallback_fn(symbol)
            return None

        attempts = 0
        wait_base = getattr(self, "ticker_backoff_base", 1.0)

        if not hasattr(self, "ticker_timeout") or self.ticker_timeout is None:
            # Kucoin precisa de timeout maior para ticker também
            if self.exchange_id == "kucoin":
                self.ticker_timeout = 30
                logger.info(
                    f"Timeout de ticker Kucoin ajustado para {self.ticker_timeout}s"
                )
            else:
                self.ticker_timeout = 15
                logger.debug(f"Timeout de ticker ajustado para {self.ticker_timeout}s")

        last_exc: Exception | None = None

        # Normaliza o símbolo para o formato da exchange (ex: BTC/USDT -> XBT/USD para Kraken)
        try:
            normalized_symbol = await normalize_symbol_async(symbol, self.exchange)
            logger.debug(
                f"Símbolo normalizado para {self.exchange_id}: {symbol} -> {normalized_symbol}"
            )
        except ValueError as norm_exc:
            logger.error(
                f"Falha ao normalizar símbolo {symbol} para {self.exchange_id}: {norm_exc}"
            )
            return None

        cached = self._get_cached_ticker(normalized_symbol)
        if cached is not None:
            logger.debug("Retornando ticker em cache para %s", normalized_symbol)
            return cached

        if self.use_websocket and self.exchange.has.get("watchTicker", False):
            try:
                ticker_ws = cast(Dict[str, Any], await self.watch_ticker(symbol))
                if ticker_ws and any(
                    k in ticker_ws for k in ("last", "close", "bid", "ask", "price")
                ):
                    self._store_ticker_cache(normalized_symbol, ticker_ws)
                    if self.event_bus and self.publish_ticker_events:
                        self.event_bus.publish(
                            "market.ticker_update",
                            {"symbol": normalized_symbol, "ticker": ticker_ws},
                        )
                    return ticker_ws
            except Exception as exc:  # pragma: no cover - network issues
                logger.warning(
                    "watch_ticker falhou para %s: %s",
                    normalized_symbol,
                    exc,
                    exc_info=True,
                )

        # Para exchanges como Kraken, garantir que estamos usando o market['id']
        # (O normalize_symbol já trata isso, mas reforçamos para rastreabilidade)
        if self.exchange_id == "kraken":
            # Buscar o market['id'] real se possível
            markets = getattr(self.exchange, "markets", {})
            if normalized_symbol in markets:
                real_id = markets[normalized_symbol].get("id", normalized_symbol)
                logger.debug(
                    f"[Kraken] Usando market['id']: {real_id} para fetch_ticker"
                )
                normalized_symbol = real_id

        # Ajustar timeout para 15 segundos se não estiver definido
        if not hasattr(self, "ticker_timeout") or self.ticker_timeout is None:
            # NETWORK FIX: KuCoin precisa de timeout muito maior devido a latência da API
            if self.exchange_id == "kucoin":
                self.ticker_timeout = 90  # NETWORK FIX: Aumentado de 30s para 90s
                logger.info(
                    f"[NETWORK FIX] Timeout de ticker KuCoin ajustado para {self.ticker_timeout}s (aumentado para resolver TimeoutError)"
                )
            else:
                self.ticker_timeout = 15
                logger.debug(f"Timeout de ticker ajustado para {self.ticker_timeout}s")

        self.last_ticker_backoff = 0.0
        while attempts < self.ticker_retries:
            try:
                sem_acquire_start = time.perf_counter()
                await self.request_semaphore.acquire()
                self.last_ticker_semaphore_wait = (
                    time.perf_counter() - sem_acquire_start
                )
                try:
                    rl_start = time.perf_counter()
                    await self._respect_rate_limit()
                    self.last_ticker_rate_wait = time.perf_counter() - rl_start
                    logger.debug(
                        "Aguardar rate limit %.2fs antes do ticker %s",
                        self.last_ticker_rate_wait,
                        normalized_symbol,
                    )
                    logger.debug(
                        "Buscando ticker para %s (tentativa %s/%s)",
                        normalized_symbol,
                        attempts + 1,
                        self.ticker_retries,
                    )
                    caps_ticker = getattr(self.exchange, "has", {})
                    if isinstance(caps_ticker, dict) and not caps_ticker.get(
                        "fetchTicker", False
                    ):
                        logger.warning(
                            "A exchange %s não suporta fetchTicker.", self.exchange_id
                        )
                        return None

                    logger.info(
                        "[fetch_ticker] Chamando fetch_ticker para: %s (timeout=%.1fs)",
                        normalized_symbol,
                        self.ticker_timeout,
                    )
                    start_time = time.perf_counter()
                    ticker_data = cast(
                        Dict[str, Any],
                        await asyncio.wait_for(
                            self.exchange.fetch_ticker(normalized_symbol),
                            timeout=self.ticker_timeout,
                        ),
                    )
                    self.last_ticker_call_time = time.perf_counter() - start_time
                finally:
                    self.request_semaphore.release()
                logger.info(
                    "[fetch_ticker] Tentativa %s/%s bem-sucedida para %s em %.2fs",
                    attempts + 1,
                    self.ticker_retries,
                    normalized_symbol,
                    self.last_ticker_call_time,
                )
                logger.debug(
                    "Latência fetch_ticker %s: %.2fs",
                    normalized_symbol,
                    self.last_ticker_call_time,
                )
                # Validação do ticker_data
                if not ticker_data or not any(
                    k in ticker_data for k in ("last", "close", "bid", "ask", "price")
                ):
                    logger.error(
                        f"Ticker inválido ou vazio para {normalized_symbol}: {ticker_data}"
                    )
                    logger.error(
                        f"Resposta bruta da API para {normalized_symbol}: {ticker_data}"
                    )
                    raise ValueError(
                        f"Ticker inválido ou vazio para {normalized_symbol}: {ticker_data}"
                    )
                logger.debug("Ticker data para %s: %s", normalized_symbol, ticker_data)
                self._store_ticker_cache(normalized_symbol, ticker_data)
                if self.event_bus and self.publish_ticker_events:
                    self.event_bus.publish(
                        "market.ticker_update",
                        {"symbol": normalized_symbol, "ticker": ticker_data},
                    )
                self.ticker_circuit.record_success()
                self._ticker_failure_count = 0
                self._record_timing(
                    "market.fetch_ticker_ms", self.last_ticker_call_time
                )
                return ticker_data
            except asyncio.TimeoutError as exc:
                self.ticker_circuit.record_failure()
                self._ticker_failure_count += 1
                self._increment_error("market.fetch_ticker_error")
                attempts += 1
                last_exc = exc
                # NETWORK FIX: Backoff exponencial mais agressivo para TimeoutError
                delay = max(self.rate_limit, min(wait_base * 2 ** attempts, 60))  # Aumentado max de 30 para 60
                logger.warning(
                    "[NETWORK FIX] Timeout ao buscar ticker %s na tentativa %s/%s após %.1fs: %s",
                    normalized_symbol,
                    attempts,
                    self.ticker_retries,
                    self.ticker_timeout,
                    exc.__class__.__name__,
                )
                if attempts < self.ticker_retries:
                    logger.info(
                        "[NETWORK FIX] Backoff exponencial de %.2fs antes de nova tentativa para %s",
                        delay,
                        normalized_symbol,
                    )
                    self.last_ticker_backoff += delay
                    await asyncio.sleep(delay)
            except asyncio.CancelledError as exc:
                # NETWORK FIX: Tratamento específico para CancelledError
                self.ticker_circuit.record_failure()
                self._ticker_failure_count += 1
                self._increment_error("market.fetch_ticker_cancelled")
                attempts += 1
                last_exc = exc
                delay = max(self.rate_limit * 2, min(wait_base * 2 ** attempts, 45))
                logger.warning(
                    "[NETWORK FIX] Operação cancelada ao buscar ticker %s na tentativa %s/%s: %s",
                    normalized_symbol,
                    attempts,
                    self.ticker_retries,
                    exc.__class__.__name__,
                )
                if attempts < self.ticker_retries:
                    logger.info(
                        "[NETWORK FIX] Aguardando %.2fs após cancelamento antes de nova tentativa para %s",
                        delay,
                        normalized_symbol,
                    )
                    self.last_ticker_backoff += delay
                    await asyncio.sleep(delay)
            except RuntimeError as exc:
                self.ticker_circuit.record_failure()
                self._ticker_failure_count += 1
                self._increment_error("market.fetch_ticker_error")
                self.ticker_circuit.record_failure()
                attempts += 1
                last_exc = exc
                delay = max(
                    self.rate_limit,
                    min(wait_base * 2**attempts, 30),
                )
                if "throttle queue is over maxCapacity" in str(exc):
                    logger.warning(
                        "Throttle queue sobrecarregada ao buscar %s; aguardando %.2fs: %s",
                        normalized_symbol,
                        self.rate_limit * 2,
                        str(exc),
                    )
                    if attempts < self.ticker_retries:
                        wait = self.rate_limit * 2
                        self.last_ticker_backoff += wait
                        await asyncio.sleep(wait)
                        continue
                logger.error(
                    "RuntimeError ao buscar ticker para %s na tentativa %s/%s: %s: %s",
                    normalized_symbol,
                    attempts,
                    self.ticker_retries,
                    type(exc).__name__,
                    exc,
                    exc_info=True,
                )
                if attempts < self.ticker_retries:
                    logger.info(
                        "Backoff de %.2fs antes de nova tentativa para %s",
                        delay,
                        normalized_symbol,
                    )
                    self.last_ticker_backoff += delay
                    await asyncio.sleep(delay)
            except Exception as exc:
                self.ticker_circuit.record_failure()
                self._ticker_failure_count += 1
                self._increment_error("market.fetch_ticker_error")
                self.ticker_circuit.record_failure()
                attempts += 1
                last_exc = exc
                if "Too many requests" in str(exc):
                    self.rate_limit = min(self.rate_limit * 1.5, 60.0)
                    delay = min(wait_base * 2**attempts, 30)
                else:
                    delay = max(
                        self.rate_limit, min(wait_base * 2 ** (attempts - 1), 30)
                    )
                import traceback

                logger.error(
                    "Falha ao buscar ticker para %s na tentativa %s/%s: %s: %s",
                    normalized_symbol,
                    attempts,
                    self.ticker_retries,
                    type(exc).__name__,
                    str(exc),
                )
                logger.debug("Stacktrace:\n%s", traceback.format_exc())
                if attempts < self.ticker_retries:
                    if "Too many requests" in str(exc):
                        logger.warning(
                            "Rate limit excedido ao buscar ticker %s. Aguardando %.2fs. Novo rate_limit: %.2fs",
                            normalized_symbol,
                            delay,
                            self.rate_limit,
                        )
                    logger.info(
                        "Backoff de %.2fs antes de nova tentativa para %s",
                        delay,
                        normalized_symbol,
                    )
                    self.last_ticker_backoff += delay
                    await asyncio.sleep(delay)

        logger.warning(
            "Erro ao buscar ticker para %s apos %s tentativas: %s: %s",
            normalized_symbol,
            self.ticker_retries,
            type(last_exc).__name__ if last_exc else "UnknownError",
            str(last_exc),
            exc_info=True,
        )
        # Fallback removido — em HFT é preferível considerar ticker indisponível a usar dado obsoleto
        self._ticker_failure_count += 1
        self._increment_error("market.fetch_ticker_error")
        self.ticker_circuit.record_failure()
        return None

    async def fetch_ticker_and_ohlcv(
        self,
        symbol: str,
        timeframe: str = "1h",
        since: Optional[int] = None,
        limit: int = 100,
    ) -> tuple[Optional[Dict[str, Any]], pd.DataFrame]:
        """Fetch ticker and OHLCV concurrently.

        Parameters
        ----------
        symbol
            Trading pair symbol.
        timeframe
            OHLCV timeframe.
        since
            Starting timestamp in milliseconds.
        limit
            Maximum number of candles to fetch.

        Returns
        -------
        tuple
            Tuple with ticker data and OHLCV DataFrame.
        """

        ticker_task = asyncio.create_task(self.fetch_ticker(symbol))
        ohlcv_task = asyncio.create_task(
            self.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
        )
        return await asyncio.gather(ticker_task, ohlcv_task)

    async def fetch_historical_data(
        self,
        spec: Union[MarketSpec, str],
        timeframe: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        use_cache: bool = True,
    ) -> pd.DataFrame:
        """
        Busca dados históricos em um intervalo de datas com paginação eficiente de forma assíncrona.
        Implementação melhorada para lidar com períodos longos, fragmentando
        o período total em chunks de 30 dias. Suporta cache local para evitar
        múltiplos downloads dos mesmos dados.

        Args:
            spec:
                Market specification or symbol string.
            timeframe:
                Timeframe string when ``spec`` is provided as a symbol.
            start_date:
                Start date of the historical data (default: 7 days ago).
            end_date:
                End date of the historical data (default: now).
            use_cache:
                Whether to use and update local cache (default: True).

        Returns:
            DataFrame pandas with historical data
        """
        # Reduzir verbosidade de logs
        # logger.debug(
        #     f"DEBUG_YAA: fetch_historical_data chamado com spec={spec!r}, timeframe={timeframe!r}"
        # )

        if isinstance(spec, MarketSpec):
            spec_obj = spec
        elif isinstance(spec, str) and isinstance(timeframe, str):
            spec_obj = MarketSpec(symbol=spec, timeframe=timeframe)
        else:
            raise TypeError(
                "fetch_historical_data requires a MarketSpec or a symbol and timeframe"
            )

        spec = spec_obj
        # logger.debug(f"DEBUG_YAA: spec_obj processado: {spec!r}")

        try:
            symbol = await normalize_symbol_async(spec.symbol, self.exchange)
            # Remover log DEBUG_YAA desnecessário
            # logger.debug(f"DEBUG_YAA: Símbolo normalizado: {symbol}")
        except ValueError as exc:
            logger.error("Símbolo inválido %s: %s", spec.symbol, exc)
            return pd.DataFrame(
                columns=["timestamp", "open", "high", "low", "close", "volume"]
            )

        # Define valores padrão para datas se não informadas
        if start_date is None:
            start_date = datetime.now(timezone.utc) - timedelta(days=7)

        if end_date is None:
            end_date = datetime.now(timezone.utc)

        # YAA: Validação do timeframe antes de processar
        # Reduzir verbosidade - manter apenas se realmente necessário
        # logger.debug(f"DEBUG_YAA: Validando spec.timeframe: {spec.timeframe!r}")
        if not spec.timeframe:
            logger.error(f"Timeframe não definido no MarketSpec: {spec}")
            return pd.DataFrame(
                columns=["timestamp", "open", "high", "low", "close", "volume"]
            )

        # Alinha datas ao timeframe para evitar requisicoes desalinhadas
        # logger.debug(
        #     f"DEBUG_YAA: Chamando _timeframe_to_pandas_freq com: {spec.timeframe!r}"
        # )
        freq = self._timeframe_to_pandas_freq(spec.timeframe)
        start_dt = pd.Timestamp(start_date).floor(freq)
        end_dt = pd.Timestamp(end_date).floor(freq)

        # Calcula timestamps em UTC
        start_ts = int(start_dt.timestamp() * 1000)
        end_ts = int(end_dt.timestamp() * 1000)

        # Nome do arquivo de cache
        symbol_clean = symbol.replace("/", "_")
        cache_filename = (
            f"{cache_dir()}/{symbol_clean}_{spec.timeframe}_{start_ts}_{end_ts}.csv"
        )

        if use_cache:
            cached = await self._load_cache(cache_filename)
            if cached is not None:
                logger.debug(
                    "Dados carregados do cache: %s | %s %s com %d candles",
                    cache_filename,
                    symbol,
                    spec.timeframe,
                    len(cached),
                )
                return cached

        # Verifica se já existem chunks parciais e tenta reutilizá-los
        if use_cache:
            chunk_size_ms = 30 * 24 * 60 * 60 * 1000  # 30 dias em milissegundos
            existing_chunks = []

            # Verifica se há chunks parciais existentes
            for i in range(math.ceil((end_ts - start_ts) / chunk_size_ms)):
                chunk_start = start_ts + i * chunk_size_ms
                chunk_end = min(end_ts, chunk_start + chunk_size_ms)
                chunk_cache = f"{cache_dir()}/{symbol_clean}_{spec.timeframe}_chunk_{chunk_start}_{chunk_end}.csv"

                chunk_df = await self._load_cache(chunk_cache)
                if chunk_df is not None and not chunk_df.empty:
                    existing_chunks.append(chunk_df)
                    logger.debug(
                        "Chunk parcial carregado do cache: %s | %s %s com %d candles",
                        chunk_cache,
                        symbol,
                        spec.timeframe,
                        len(chunk_df),
                    )

            if existing_chunks:
                # Combina chunks existentes
                df = pd.concat(existing_chunks, ignore_index=True)
                df = df.drop_duplicates(subset="timestamp").sort_values("timestamp")

                # Verifica se já temos cobertura completa
                if (
                    df["timestamp"].min() <= start_dt
                    and df["timestamp"].max() >= end_dt
                ):
                    logger.info(
                        "Cache completo encontrado. Reutilizando dados existentes."
                    )
                    return df

        # Calcula aproximadamente quantos candles precisamos buscar com base no
        # timeframe
        timeframe_minutes = timeframe_to_minutes(spec.timeframe)
        timeframe_ms = self._timeframe_to_ms(spec.timeframe)
        expected_candles = (end_ts - start_ts) / timeframe_ms
        expected_days = (end_ts - start_ts) / (24 * 60 * 60 * 1000)

        # Define tamanho de lote conforme limite da exchange
        batch_size = self._max_ohlcv(spec.timeframe)
        period_ms = end_ts - start_ts
        max_per_call = timeframe_ms * batch_size
        if period_ms < max_per_call:
            batch_size = math.ceil(period_ms / timeframe_ms)

        logger.debug(
            f"Buscando aproximadamente {expected_candles:.0f} candles para {symbol} "
            f"({spec.timeframe}) cobrindo {expected_days:.1f} dias"
        )

        if expected_candles <= batch_size:
            df = await self.fetch_ohlcv(
                MarketSpec(symbol=symbol, timeframe=spec.timeframe),
                since=start_ts,
                limit=int(expected_candles),
            )
            if use_cache:
                await self._save_cache(df, cache_filename)
            return df

        try:
            # Se o período total for maior que 30 dias, dividimos em chunks de 30 dias
            # para garantir que possamos obter dados de períodos mais longos

            # Calcular o número de chunks necessários
            chunk_size_ms = 30 * 24 * 60 * 60 * 1000  # 30 dias em milissegundos
            total_period_ms = end_ts - start_ts
            num_chunks = total_period_ms // chunk_size_ms
            if total_period_ms % chunk_size_ms:
                num_chunks += 1
            num_chunks = max(1, num_chunks)

            # Se for necessário apenas um chunk, usamos o método tradicional
            if num_chunks == 1:
                return await self._fetch_historical_chunk(
                    symbol, spec.timeframe, start_ts, end_ts, batch_size
                )

            # Caso contrário, dividimos em múltiplos chunks e combinamos os
            # resultados
            logger.info(
                f"Dividindo consulta em {num_chunks} chunks de 30 dias para obter dados de {expected_days:.1f} dias"
            )

            dfs = []
            for i in range(num_chunks):
                chunk_start = start_ts + i * chunk_size_ms
                chunk_end = min(end_ts, chunk_start + chunk_size_ms)

                # Não continuar se já passamos do final do período
                if chunk_start >= end_ts:
                    break

                logger.info(
                    f"Buscando chunk {i + 1}/{num_chunks}: "
                    + f"{datetime.fromtimestamp(chunk_start / 1000).strftime('%Y-%m-%d')} a "
                    + f"{datetime.fromtimestamp(chunk_end / 1000).strftime('%Y-%m-%d')}"
                )

                # Aguarda apenas o necessário para respeitar o rate-limit (≥3 s para Kraken)
                pause = max(self.rate_limit * 1.2, 3.5)
                logger.info(
                    "Aguardando %.2f segundos entre chunks para respeitar rate limit...",
                    pause,
                )
                await asyncio.sleep(pause)

                df_chunk = await self._fetch_historical_chunk(
                    symbol, spec.timeframe, chunk_start, chunk_end, batch_size
                )

                if df_chunk is not None and not df_chunk.empty:
                    dfs.append(df_chunk)
                    logger.info(
                        "Chunk %d/%d obtido com %d registros",
                        i + 1,
                        num_chunks,
                        len(df_chunk),
                    )
                else:
                    logger.warning(
                        "Chunk %d/%d vazio. Continuando...", i + 1, num_chunks
                    )

            # Combina todos os dataframes e remove duplicatas
            if not dfs:
                return pd.DataFrame(
                    columns=["timestamp", "open", "high", "low", "close", "volume"]
                )

            df = pd.concat(dfs, ignore_index=True)
            df = (
                df.drop_duplicates(subset="timestamp")
                .sort_values("timestamp")
                .reset_index(drop=True)
            )

            # Log final com mais detalhes
            days_coverage = (
                df["timestamp"].max() - df["timestamp"].min()
            ).total_seconds() / (24 * 3600)
            date_min = df["timestamp"].min().strftime("%Y-%m-%d %H:%M:%S")
            date_max = df["timestamp"].max().strftime("%Y-%m-%d %H:%M:%S")
            logger.info(
                f"Dados históricos completos: {len(df)} registros cobrindo {days_coverage:.1f} dias"
            )
            logger.info(f"Período obtido: {date_min} a {date_max}")

            # Log de diagnóstico - verificar se tivemos problemas de cobertura
            period_requested = (end_ts - start_ts) / (24 * 3600 * 1000)
            coverage_pct = (days_coverage / period_requested) * 100
            logger.info(
                f"Cobertura do período solicitado: {coverage_pct:.1f}% "
                f"({days_coverage:.1f}/{period_requested:.1f} dias)"
            )

            if use_cache:
                logger.info("Salvando dados no cache: %s", cache_filename)
                await self._save_cache(df, cache_filename)

                # Salva também os chunks individuais
                chunk_size_ms = 30 * 24 * 60 * 60 * 1000
                for i in range(num_chunks):
                    chunk_start = start_ts + i * chunk_size_ms
                    chunk_end = min(end_ts, chunk_start + chunk_size_ms)
                    chunk_cache = f"{cache_dir()}/{symbol_clean}_{spec.timeframe}_chunk_{chunk_start}_{chunk_end}.csv"

                    # YAA: Garantir que as comparações de timestamp sejam consistentes
                    chunk_start_ts = pd.Timestamp(chunk_start, unit="ms")
                    chunk_end_ts = pd.Timestamp(chunk_end, unit="ms")

                    # Converter para o mesmo tipo de timezone
                    if df["timestamp"].dt.tz is None:
                        # Se df timestamp é naive, converter chunks para naive
                        chunk_start_ts = chunk_start_ts.tz_localize(None)
                        chunk_end_ts = chunk_end_ts.tz_localize(None)
                    else:
                        # Se df timestamp é aware, garantir que chunks também sejam UTC
                        chunk_start_ts = chunk_start_ts.tz_localize("UTC")
                        chunk_end_ts = chunk_end_ts.tz_localize("UTC")

                    chunk_df = df[
                        (df["timestamp"] >= chunk_start_ts)
                        & (df["timestamp"] < chunk_end_ts)
                    ]
                    if not chunk_df.empty:
                        await self._save_cache(chunk_df, chunk_cache)
                        logger.info(
                            "Chunk %d/%d salvo no cache: %s",
                            i + 1,
                            num_chunks,
                            chunk_cache,
                        )

            return df

        except Exception as e:
            logger.error(f"Erro ao buscar dados históricos para {symbol}: {str(e)}")
            raise

    async def _fetch_historical_chunk(
        self, symbol: str, timeframe: str, start_ts: int, end_ts: int, batch_size: int
    ) -> pd.DataFrame:
        """Busca um único chunk de dados históricos, paginando se necessário."""
        # Alinha timestamps ao timeframe para consistência
        freq = self._timeframe_to_pandas_freq(timeframe)
        start_ts = int(pd.Timestamp(start_ts, unit="ms").floor(freq).timestamp() * 1000)
        end_ts = int(pd.Timestamp(end_ts, unit="ms").floor(freq).timestamp() * 1000)

        # O resto da função permanece o mesmo...
        columns = ["timestamp", "open", "high", "low", "close", "volume"]
        all_data = await self._paginate_ohlcv(
            MarketSpec(symbol=symbol, timeframe=timeframe),
            start_ts,
            end_ts,
            batch_size,
        )

        if not all_data:
            logger.error(
                "Nenhum dado histórico encontrado para %s no período especificado.",
                symbol,
            )
            return pd.DataFrame(columns=columns)

        df = _to_dataframe(all_data, columns)
        df = df.sort_values("timestamp").reset_index(drop=True)

        days_coverage = (
            df["timestamp"].max() - df["timestamp"].min()
        ).total_seconds() / (24 * 3600)
        period_requested = (end_ts - start_ts) / (24 * 3600 * 1000)
        coverage_pct = (days_coverage / period_requested) * 100
        logger.info(
            "Dados históricos obtidos: %s registros cobrindo %.1f dias (%.1f%%)",
            len(df),
            days_coverage,
            coverage_pct,
        )

        await self._save_cache(
            df,
            f"{cache_dir()}/{symbol.replace('/', '_')}_{timeframe}_{start_ts}_{end_ts}.csv",
        )
        return df

    def _timeframe_to_ms(self, timeframe: str) -> int:
        """Return timeframe length in milliseconds."""

        return timeframe_to_milliseconds(timeframe)

    def _timeframe_to_pandas_freq(self, timeframe: str) -> str:
        """Return pandas frequency string for ``timeframe``.

        Parameters
        ----------
        timeframe : str
            Timeframe no formato ``"1m"``, ``"1h"`` etc.

        Returns
        -------
        str
            Frequência compatível com pandas.
        """

        try:
            return timeframe_to_pandas_freq(timeframe)
        except ValueError:
            logger.error(f"Timeframe inválido fornecido: {repr(timeframe)}")
            raise

    async def fetch_account_info(self) -> Dict[str, Any]:
        """
        Busca informações da conta na exchange de forma assíncrona.

        Returns:
            Dicionário com informações da conta
        """
        if not self.exchange.apiKey:
            raise ValueError(
                "API Key não configurada. Autenticação necessária para esta operação."
            )

        try:
            await self._respect_rate_limit()
            balance = cast(Dict[str, Any], await self.exchange.fetch_balance())

            # Extrai informações relevantes
            account_info: Dict[str, Any] = {
                "total_balance_usd": 0,  # Será calculado abaixo
                "free_balances": {
                    k: v
                    for k, v in cast(Dict[str, float], balance["free"]).items()
                    if v > 0
                },
                "used_balances": {
                    k: v
                    for k, v in cast(Dict[str, float], balance["used"]).items()
                    if v > 0
                },
                "total_balances": {
                    k: v
                    for k, v in cast(Dict[str, float], balance["total"]).items()
                    if v > 0
                },
            }

            # Tenta calcular o valor total em USD utilizando chamadas de ticker
            # concorrentes para evitar bloqueios sequenciais.
            try:
                usd_value = 0.0
                ticker_tasks: dict[str, asyncio.Task[Optional[Dict[str, Any]]]] = {}
                for currency, amount in account_info["total_balances"].items():
                    if currency in ("USD", "USDT"):
                        usd_value += amount
                    else:
                        ticker_tasks[currency] = asyncio.create_task(
                            self.fetch_ticker(f"{currency}/USD")
                        )

                results = await asyncio.gather(
                    *ticker_tasks.values(), return_exceptions=True
                )
                for (currency, amount), result in zip(
                    account_info["total_balances"].items(), results
                ):
                    if currency in ticker_tasks and isinstance(result, dict):
                        last = cast(Dict[str, Any], result).get("last")
                        if last is not None:
                            usd_value += amount * float(last)

                account_info["total_balance_usd"] = usd_value
            except BaseException as exc:
                # Em caso de falha, não interrompe a execução e retorna valores parciais.
                logger.exception(
                    "Erro ao calcular saldo total em USD: %s",
                    exc,
                )

            return account_info

        except (ccxt.BaseError, asyncio.TimeoutError) as e:
            logger.error(f"Erro ao buscar informações da conta: {str(e)}")
            raise

    async def place_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Executa uma ordem na exchange de forma assíncrona.

        Args:
            symbol: Par de trading (ex: BTC/USD)
            order_type: Tipo de ordem (market, limit)
            side: Lado da ordem (buy, sell)
            amount: Quantidade a comprar/vender
            price: Preço limite (apenas para ordens limit)
            params: Parâmetros adicionais para a exchange

        Returns:
            Dicionário com informações da ordem executada
        """
        symbol = await normalize_symbol_async(symbol, self.exchange)

        if not self.exchange.apiKey:
            raise ValueError(
                "API Key não configurada. Autenticação necessária para esta operação."
            )

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "order.place",
            attributes={
                "symbol": symbol,
                "side": side,
                "type": order_type,
                "amount": amount,
                "exchange": self.exchange_id,
            },
        ):
            try:
                await self._respect_rate_limit()

                params = params or {}

                # Cria a ordem
                if order_type == "limit" and price is not None:
                    order = cast(
                        Dict[str, Any],
                        await self.exchange.create_order(
                            symbol, order_type, side, amount, price, params
                        ),
                    )
                else:
                    order = cast(
                        Dict[str, Any],
                        await self.exchange.create_order(
                            symbol, order_type, side, amount, params=params
                        ),
                    )

                logger.info(
                    f"Ordem executada: {order['id']} - {symbol} {side} {amount} @ {price if price else 'mercado'}"
                )
                return order

            except Exception as e:
                logger.error(
                    f"Erro ao executar ordem {side} {amount} {symbol}: {str(e)}"
                )
                if self.event_bus:
                    self.event_bus.publish(
                        "exchange.lifecycle",
                        {
                            "exchange": self.exchange_id,
                            "event_type": "order_failure",
                            "error": str(e),
                        },
                    )
                raise

    async def create_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Compatibilidade com código legado."""
        return await self.place_order(symbol, order_type, side, amount, price, params)

    async def cancel_order(
        self, order_id: str, symbol: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Cancela uma ordem na exchange de forma assíncrona.

        Args:
            order_id: ID da ordem a cancelar
            symbol: Par de trading (necessário em algumas exchanges)

        Returns:
            Dicionário com informações do cancelamento. Retorna
            um dicionário vazio caso o símbolo fornecido seja inválido.
        """
        if symbol is not None:
            try:
                symbol = await normalize_symbol_async(symbol, self.exchange)
            except ValueError as exc:
                logger.error("Símbolo inválido %s: %s", symbol, exc)
                return cast(Dict[str, Any], {})

        if not self.exchange.apiKey:
            raise ValueError(
                "API Key não configurada. Autenticação necessária para esta operação."
            )

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "order.cancel",
            attributes={
                "order_id": order_id,
                "symbol": symbol or "",
                "exchange": self.exchange_id,
            },
        ):
            try:
                await self._respect_rate_limit()

                # Cancela a ordem
                result = cast(
                    Dict[str, Any], await self.exchange.cancel_order(order_id, symbol)
                )

                logger.info(f"Ordem cancelada: {order_id}")
                return result

            except Exception as e:
                logger.error(f"Erro ao cancelar ordem {order_id}: {str(e)}")
                raise

    async def fetch_order(
        self, order_id: str, symbol: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Busca informações de uma ordem específica de forma assíncrona.

        Args:
            order_id: ID da ordem
            symbol: Par de trading (necessário em algumas exchanges)

        Returns:
            Dicionário com informações da ordem. Retorna um
            dicionário vazio caso o símbolo informado seja inválido.
        """
        if symbol is not None:
            try:
                symbol = await normalize_symbol_async(symbol, self.exchange)
            except ValueError as exc:
                logger.error("Símbolo inválido %s: %s", symbol, exc)
                return cast(Dict[str, Any], {})

        if not self.exchange.apiKey:
            raise ValueError(
                "API Key não configurada. Autenticação necessária para esta operação."
            )

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "order.fetch",
            attributes={
                "order_id": order_id,
                "symbol": symbol or "",
                "exchange": self.exchange_id,
            },
        ):
            try:
                await self._respect_rate_limit()

                # Busca a ordem
                order = cast(
                    Dict[str, Any], await self.exchange.fetch_order(order_id, symbol)
                )

                return order

            except Exception as e:
                logger.error(f"Erro ao buscar ordem {order_id}: {str(e)}")
                raise

    async def fetch_open_orders(
        self, symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Busca ordens abertas de forma assíncrona.

        Args:
            symbol: Par de trading (opcional)

        Returns:
            Lista de ordens abertas
        """
        if symbol is not None:
            try:
                symbol = await normalize_symbol_async(symbol, self.exchange)
            except ValueError as exc:
                logger.error("Símbolo inválido %s: %s", symbol, exc)
                return []

        if not self.exchange.apiKey:
            raise ValueError(
                "API Key não configurada. Autenticação necessária para esta operação."
            )

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "order.fetch_open",
            attributes={"symbol": symbol or "", "exchange": self.exchange_id},
        ):
            try:
                await self._respect_rate_limit()

                # Busca ordens abertas
                orders = cast(
                    List[Dict[str, Any]], await self.exchange.fetch_open_orders(symbol)
                )

                return orders

            except Exception as e:
                logger.error(f"Erro ao buscar ordens abertas: {str(e)}")
                raise

    async def fetch_positions(
        self, symbols: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Retorna posições abertas da exchange.

        Quando ``fetchPositions`` não é suportado, ``[]`` é retornado e cabe ao
        ``PositionManager`` reconstruir o estado consultando ``fetch_open_orders``
        ou o histórico local de trades.

        Parameters
        ----------
        symbols : list[str], optional
            Símbolos a filtrar ao solicitar as posições.

        Returns
        -------
        list[dict]
            Lista de posições abertas.
        """
        if self.fetch_positions_supported is False:
            return []

        if self.fetch_positions_supported is None:
            self.fetch_positions_supported = bool(
                self.exchange.has.get("fetchPositions")
            )
            if not self.fetch_positions_supported:
                logger.warning(
                    "A exchange %s não suporta fetchPositions.", self.exchange_id
                )
                return []

        if symbols:
            normalized: List[str] = []
            for s in symbols:
                try:
                    normalized.append(await normalize_symbol_async(s, self.exchange))
                except ValueError as exc:
                    logger.error("Símbolo inválido %s: %s", s, exc)
            symbols = normalized
        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "order.fetch_positions",
            attributes={"exchange": self.exchange_id},
        ):
            try:
                await self._respect_rate_limit()
                logger.info(f"Buscando posições para os símbolos: {symbols}")
                positions = await self.exchange.fetch_positions(symbols)
                logger.info(f"Encontradas {len(positions)} posições na exchange.")
                open_positions = [
                    p
                    for p in positions
                    if p.get("contracts", 0) != 0 or p.get("size", 0) != 0
                ]
                logger.info(
                    f"Encontradas {len(open_positions)} posições com tamanho diferente de zero."
                )
                return cast(List[Dict[str, Any]], open_positions)
            except Exception as e:
                logger.error(f"Erro ao buscar posições: {e}", exc_info=True)
                return []

    async def fetch_order_book(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Retorna o livro de ordens da exchange via REST.

        Parameters
        ----------
        symbol : str
            Par de trading.
        limit : int, optional
            Quantidade máxima de níveis a retornar.

        Returns
        -------
        dict[str, Any]
            Estrutura do livro de ordens.

        Raises
        ------
        NotImplementedError
            Se a exchange não suportar ``fetchOrderBook``.
        """

        caps = getattr(self.exchange, "has", {})
        if not caps.get("fetchOrderBook", False):
            raise NotImplementedError("Exchange não suporta fetchOrderBook")

        try:
            symbol = await normalize_symbol_async(symbol, self.exchange)
        except Exception as exc:
            logger.error("Símbolo inválido %s: %s", symbol, exc)
            raise

        await self._respect_rate_limit()
        result = await self.exchange.fetch_order_book(symbol, limit=limit)
        return cast(Dict[str, Any], result)

    async def fetch_trades(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Retorna os trades recentes via REST.

        Parameters
        ----------
        symbol : str
            Par de trading.
        limit : int, optional
            Quantidade de trades a retornar.

        Returns
        -------
        list[dict[str, Any]]
            Lista de trades recentes.

        Raises
        ------
        NotImplementedError
            Se a exchange não suportar ``fetchTrades``.
        """

        caps = getattr(self.exchange, "has", {})
        if not caps.get("fetchTrades", False):
            raise NotImplementedError("Exchange não suporta fetchTrades")

        try:
            symbol = await normalize_symbol_async(symbol, self.exchange)
        except Exception as exc:
            logger.error("Símbolo inválido %s: %s", symbol, exc)
            raise

        await self._respect_rate_limit()
        trades = await self.exchange.fetch_trades(symbol, limit=limit)
        return cast(List[Dict[str, Any]], trades)

    async def calculate_volatility(
        self, symbol: str, timeframe: str = "1h", window: int = 24
    ) -> float:
        """
        Calcula a volatilidade de um ativo de forma assíncrona.

        Args:
            symbol: Par de trading (ex: BTC/USD)
            timeframe: Intervalo de tempo
            window: Janela para cálculo da volatilidade (em períodos)

        Returns:
            Volatilidade como desvio padrão anualizado
        """
        func_name = inspect.currentframe().f_code.co_name
        logger.debug(f"[{func_name}] Timeframe recebido: {timeframe}")
        try:
            # Busca dados históricos
            data = await self.fetch_ohlcv(symbol, timeframe, limit=window + 10)

            # Calcula retornos logarítmicos
            data["log_return"] = np.log(data["close"] / data["close"].shift(1))

            # Calcula volatilidade (desvio padrão dos retornos)
            std = data["log_return"].std()

            # Anualiza a volatilidade
            if timeframe == "1m":
                annualized = std * np.sqrt(525600)  # 365 * 24 * 60
            elif timeframe == "5m":
                annualized = std * np.sqrt(105120)  # 365 * 24 * 12
            elif timeframe == "15m":
                annualized = std * np.sqrt(35040)  # 365 * 24 * 4
            elif timeframe == "30m":
                annualized = std * np.sqrt(17520)  # 365 * 24 * 2
            elif timeframe == "1h":
                annualized = std * np.sqrt(8760)  # 365 * 24
            elif timeframe == "4h":
                annualized = std * np.sqrt(2190)  # 365 * 6
            elif timeframe == "1d":
                annualized = std * np.sqrt(365)
            else:
                # YAA: Fallback para timeframes não padrão para evitar UnboundLocalError
                logger.warning(
                    f"Timeframe não padrão '{timeframe}' para anualização. Retornando std não anualizado."
                )
                annualized = std

            return float(annualized)

        except (
            asyncio.TimeoutError,
            CCXTNetworkError,
            CCXTExchangeError,
            Exception,
        ) as e:
            logger.error(f"Erro ao calcular volatilidade para {symbol}: {str(e)}")
            raise

    async def add_trading_pair(self, symbol: str) -> None:
        """Adiciona um par de trading garantindo mercados em cache."""

        if not self.markets_loaded or not self.exchange.markets:
            markets = await self.exchange.load_markets()
            self.markets_loaded = True
        else:
            markets = self.exchange.markets
        if symbol not in markets:
            markets = await self.exchange.load_markets()
            if symbol not in markets:
                logger.warning("Par de trading %s não disponível na Kraken", symbol)
                return
        logger.info("Par de trading adicionado para monitoramento: %s", symbol)

    async def get_earliest_timestamp(self, symbol: str, timeframe: str) -> pd.Timestamp:
        """Retorna o timestamp do primeiro candle disponível.

        A função consulta ``fetch_ohlcv`` com ``since=0`` para recuperar o
        candle mais antigo. O valor retornado é convertido para ``pd.Timestamp``.

        Parameters
        ----------
        symbol : str
            Par de trading no formato suportado pelo ``ccxt``.
        timeframe : str
            Intervalo de tempo (ex.: ``"1h"``).

        Returns
        -------
        pd.Timestamp
            Timestamp do primeiro candle conhecido em UTC.

        Raises
        ------
        InsufficientHistoryError
            Se nenhum dado histórico puder ser encontrado.
        """

        try:
            normalized = await normalize_symbol_async(symbol, self.exchange)
        except Exception as exc:
            logger.warning("Falha ao normalizar simbolo %s: %s", symbol, exc)
            normalized = symbol

        now = time.time()
        last_fail = self._earliest_failure_ts.get(normalized, {}).get(timeframe)
        if last_fail is not None and now - last_fail < self.earliest_timestamp_cooldown:
            logger.info(
                "Cooldown ativo para %s em %s; consultando cache", normalized, timeframe
            )
            symbol_clean = normalized.replace("/", "_")
            prefix = f"{symbol_clean}_{timeframe}"
            earliest: pd.Timestamp | None = None
            total = 0
            for fname in os.listdir(cache_dir()):
                if not fname.startswith(prefix) or not fname.endswith(".csv"):
                    continue
                cached = await self._load_cache(os.path.join(cache_dir(), fname))
                if cached is None or cached.empty:
                    continue
                total += len(cached)
                ts = pd.to_datetime(cached["timestamp"].min(), utc=True)
                if earliest is None or ts < earliest:
                    earliest = ts
            if earliest is not None:
                logger.debug(
                    "Timestamp inicial de %s em %s obtido do cache (%d candles)",
                    normalized,
                    timeframe,
                    total,
                )
                return earliest
            self._increment_error("market.earliest_timestamp_empty")
            raise InsufficientHistoryError(
                f"Sem histórico disponível para {normalized} em {timeframe}"
            )

        try:
            orig_retries = self.ohlcv_retries
            self.ohlcv_retries = 1
            df = await self.fetch_ohlcv(normalized, timeframe, since=0, limit=1)
        except (
            asyncio.TimeoutError,
            CCXTNetworkError,
            CCXTExchangeError,
        ) as exc:
            logger.warning(
                "Erro na tentativa since=0 para %s em %s: %s",
                normalized,
                timeframe,
                exc,
            )
            df = pd.DataFrame()
        finally:
            self.ohlcv_retries = orig_retries

        if df.empty:
            logger.info(
                "Primeira tentativa since=0 vazia para %s em %s; aplicando fallback",
                normalized,
                timeframe,
            )
            if self.exchange_id == "kucoin":
                try:
                    params = {
                        "endAt": int(time.time() * 1000),
                        "limit": self._max_ohlcv(timeframe),
                    }
                    async with lock:
                        raw = await self.exchange.fetch_ohlcv(
                            normalized, timeframe, params=params
                        )
                    df = _to_dataframe(
                        raw,
                        ["timestamp", "open", "high", "low", "close", "volume"],
                    ).sort_values("timestamp")
                except Exception as exc:  # pragma: no cover - network issues
                    logger.warning("Tentativa endAt falhou: %s", exc)
                    df = pd.DataFrame()
            else:
                try:
                    hist = await self.fetch_historical_data(
                        MarketSpec(symbol=normalized, timeframe=timeframe)
                    )
                    if hist is not None and not hist.empty:
                        return pd.to_datetime(hist["timestamp"].min(), utc=True)
                except (
                    asyncio.TimeoutError,
                    CCXTNetworkError,
                    CCXTExchangeError,
                ) as exc:  # pragma: no cover - network issues
                    logger.warning(
                        "fetch_historical_data falhou para %s em %s: %s",
                        normalized,
                        timeframe,
                        exc,
                    )

        if df.empty:
            logger.warning(
                "fetch_ohlcv retornou vazio para %s em %s; verificando cache",
                normalized,
                timeframe,
            )
            self._earliest_failure_ts[normalized][timeframe] = time.time()
            symbol_clean = normalized.replace("/", "_")
            prefix = f"{symbol_clean}_{timeframe}"
            earliest: pd.Timestamp | None = None
            total = 0
            for fname in os.listdir(cache_dir()):
                if not fname.startswith(prefix) or not fname.endswith(".csv"):
                    continue
                cached = await self._load_cache(os.path.join(cache_dir(), fname))
                if cached is None or cached.empty:
                    continue
                total += len(cached)
                ts = pd.to_datetime(cached["timestamp"].min(), utc=True)
                if earliest is None or ts < earliest:
                    earliest = ts
            if earliest is not None:
                logger.debug(
                    "Timestamp inicial de %s em %s encontrado no cache (%d candles)",
                    normalized,
                    timeframe,
                    total,
                )
                return earliest
            logger.warning(
                "Nenhum arquivo de cache válido encontrado para %s em %s",
                normalized,
                timeframe,
            )
            self._increment_error("market.earliest_timestamp_empty")
            raise InsufficientHistoryError(
                f"Sem histórico disponível para {normalized} em {timeframe}"
            )
        ts = pd.to_datetime(df["timestamp"].iloc[0], utc=True)

        now_ts = pd.Timestamp.utcnow()
        timeframe_seconds = timeframe_to_minutes(timeframe) * 60
        threshold_env = os.getenv("EARLIEST_TIMESTAMP_THRESHOLD")
        try:
            threshold = float(threshold_env) if threshold_env else 0.0
        except ValueError:
            threshold = 0.0
        if threshold <= 0:
            threshold = timeframe_seconds

        if (now_ts - ts).total_seconds() < threshold:
            logger.info(
                "Timestamp inicial %s em %s eh recente; buscando historico retroativo",
                normalized,
                timeframe,
            )
            lookback = timedelta(days=30)
            end_date = now_ts
            start_date = end_date - lookback
            earliest: pd.Timestamp | None = None
            for _ in range(12):
                hist = await self.fetch_historical_data(
                    spec,
                    start_date=start_date,
                    end_date=end_date,
                )
                if hist is None or hist.empty:
                    break
                cand_min = pd.to_datetime(hist["timestamp"].min(), utc=True)
                earliest = (
                    cand_min if earliest is None or cand_min < earliest else earliest
                )
                if cand_min >= start_date:
                    break
                end_date = start_date
                start_date -= lookback
            if earliest is not None:
                logger.info(
                    "Timestamp inicial de %s em %s encontrado via busca retroativa: %s",
                    normalized,
                    timeframe,
                    earliest,
                )
                return earliest
            self._increment_error("market.earliest_timestamp_empty")
            raise InsufficientHistoryError(
                f"Sem histórico disponível para {normalized} em {timeframe}"
            )

        logger.info(
            "Timestamp inicial de %s em %s obtido da exchange: %s",
            normalized,
            timeframe,
            ts,
        )
        return ts

    async def max_history_candles(self, symbol: str, timeframe: str) -> int:
        """Calcula quantos candles estão disponíveis desde o primeiro registro."""

        try:
            earliest = await self.get_earliest_timestamp(symbol, timeframe)
        except InsufficientHistoryError:
            logger.warning(
                "Impossivel determinar timestamp inicial para %s em %s",
                symbol,
                timeframe,
            )
            raise

        delta = pd.Timestamp.utcnow() - earliest
        minutes = timeframe_to_minutes(timeframe)
        count = int(delta.total_seconds() // (minutes * 60))
        logger.debug(
            "%d candles detectados para %s em %s desde %s",
            count,
            symbol,
            timeframe,
            earliest,
        )
        return max(0, count)

    async def _auto_tune_batch_size(
        self, timeframe: str, elapsed_time: float, data_count: int
    ) -> None:
        """Ajusta dinamicamente o batch_size baseado na latência observada.

        Parameters
        ----------
        timeframe : str
            Timeframe da requisição
        elapsed_time : float
            Tempo total da requisição em segundos
        data_count : int
            Número de registros retornados
        """
        if data_count <= 0:
            return

        # Calcula latência média por registro
        avg_latency_per_record = elapsed_time / data_count

        # Atualiza histórico de latência
        self._last_request_latency[timeframe] = avg_latency_per_record

        # Obtém batch_size atual
        current_batch = self._adaptive_batch_sizes.get(
            timeframe, self._max_ohlcv(timeframe)
        )

        # Define thresholds de latência (em segundos por registro)
        target_latency = 0.001  # 1ms por registro ideal
        max_latency = 0.003  # 3ms por registro máximo

        # Ajusta batch_size baseado na latência
        if avg_latency_per_record < target_latency:
            # Latência baixa - pode aumentar batch
            new_batch = min(int(current_batch * 1.5), 1500)  # Limite máximo de 1500
            if new_batch > current_batch:
                logger.info(
                    f"📈 Auto-tuning: Aumentando batch_size para {timeframe} "
                    f"de {current_batch} para {new_batch} (latência: {avg_latency_per_record:.3f}s/rec)"
                )
        elif avg_latency_per_record > max_latency:
            # Latência alta - reduz batch
            new_batch = max(int(current_batch * 0.7), 200)  # Mínimo de 200
            if new_batch < current_batch:
                logger.info(
                    f"📉 Auto-tuning: Reduzindo batch_size para {timeframe} "
                    f"de {current_batch} para {new_batch} (latência: {avg_latency_per_record:.3f}s/rec)"
                )
        else:
            # Latência aceitável - mantém
            new_batch = current_batch
            logger.debug(
                f" Auto-tuning: Mantendo batch_size {current_batch} para {timeframe} "
                f"(latência: {avg_latency_per_record:.3f}s/rec)"
            )

        # Atualiza batch_size adaptativo
        self._adaptive_batch_sizes[timeframe] = new_batch
